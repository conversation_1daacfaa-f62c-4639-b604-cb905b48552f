//+------------------------------------------------------------------+
//|                                            Enhanced_V75s_EA.mq5 |
//|                                       Copyright 2024, Your Name   |
//|                                             https://www.mql5.com/ |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      "https://www.mql5.com"
#property version   "2.00"
#property strict

// Error code constants
#define ERR_INVALID_STOPS 130

// Input Parameters
input ENUM_TIMEFRAMES InpTimeframe = PERIOD_M5;  // Trading Timeframe (Changed to M5 for better signals)
input int    InpEMAPeriod     = 21;      // EMA Period
input int    InpRSIPeriod     = 14;      // RSI Period
input int    InpMACDFast      = 12;      // MACD Fast Period
input int    InpMACDSlow      = 26;      // MACD Slow Period
input int    InpMACDSignal    = 9;       // MACD Signal Period
input int    InpBBPeriod      = 20;      // Bollinger Bands Period
input double InpLotSize       = 0.01;    // Lot Size
input int    InpStopLoss      = 800;     // Stop Loss (points)
input int    InpTakeProfit    = 1200;    // Take Profit (points)
input bool   InpUseStops      = true;    // Use Stop Loss and Take Profit
input double InpRiskReward    = 1.5;     // Risk Reward Ratio (TP/SL)
input int    InpMinCandlesGap = 5;       // Minimum candles between trades
input bool   InpUseTrendFilter = true;   // Use trend filter
input int    InpTrendPeriod   = 50;      // Trend filter EMA period
input bool   InpUseAtrStops   = true;    // Use ATR for Stop Loss and Take Profit
input int    InpAtrPeriod     = 14;      // ATR Period for stops
input double InpAtrSlMultiplier = 2.0;   // ATR Multiplier for Stop Loss
input double InpAtrTpMultiplier = 3.0;   // ATR Multiplier for Take Profit


// Global Variables
int    handleEMA;                        // EMA indicator handle
int    handleRSI;                        // RSI indicator handle
int    handleMACD;                       // MACD indicator handle
int    handleBB;                         // Bollinger Bands handle
int    handleTrendEMA;                   // Trend filter EMA handle
int    handleATR;                        // ATR indicator handle
double emaBuffer[];                      // EMA values buffer
double rsiBuffer[];                      // RSI values buffer
double macdMainBuffer[];                 // MACD main line buffer
double macdSignalBuffer[];               // MACD signal line buffer
double bbUpperBuffer[];                  // BB upper band buffer
double bbLowerBuffer[];                  // BB lower band buffer
double bbMiddleBuffer[];                 // BB middle band buffer
double trendEmaBuffer[];                 // Trend EMA buffer
double atrBuffer[];                      // ATR values buffer

// Statistics Variables
int    totalTrades = 0;
int    winningTrades = 0;
int    losingTrades = 0;
double totalProfit = 0.0;
double totalLoss = 0.0;
datetime lastTradeTime = 0;
double initialBalance = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                     |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("Enhanced V75s EA v2.0 - Starting initialization...");
    Print("EA attached to symbol: ", _Symbol);
    
    // Store initial balance
    initialBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    
    // Print symbol specifications for debugging
    double points = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    long stopLevel = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
    double spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * points;
    
    Print("Symbol Point: ", points);
    Print("Stop Level: ", stopLevel, " points");
    Print("Current Spread: ", spread);
    Print("Initial Balance: $", initialBalance);
    
    // Initialize indicator handles
    handleEMA = iMA(_Symbol, InpTimeframe, InpEMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
    handleRSI = iRSI(_Symbol, InpTimeframe, InpRSIPeriod, PRICE_CLOSE);
    handleMACD = iMACD(_Symbol, InpTimeframe, InpMACDFast, InpMACDSlow, InpMACDSignal, PRICE_CLOSE);
    handleBB = iBands(_Symbol, InpTimeframe, InpBBPeriod, 0, 2.0, PRICE_CLOSE);
    handleTrendEMA = iMA(_Symbol, InpTimeframe, InpTrendPeriod, 0, MODE_EMA, PRICE_CLOSE);
    handleATR = iATR(_Symbol, InpTimeframe, InpAtrPeriod);

    // Validate indicator handles
    if(handleEMA == INVALID_HANDLE || handleRSI == INVALID_HANDLE || 
       handleMACD == INVALID_HANDLE || handleBB == INVALID_HANDLE || 
       handleTrendEMA == INVALID_HANDLE || handleATR == INVALID_HANDLE)
    {
        Print("Error initializing indicators!");
        return INIT_FAILED;
    }

    
    // Initialize arrays
    ArraySetAsSeries(emaBuffer, true);
    ArraySetAsSeries(rsiBuffer, true);
    ArraySetAsSeries(macdMainBuffer, true);
    ArraySetAsSeries(macdSignalBuffer, true);
    ArraySetAsSeries(bbUpperBuffer, true);
    ArraySetAsSeries(bbLowerBuffer, true);
    ArraySetAsSeries(bbMiddleBuffer, true);
    ArraySetAsSeries(trendEmaBuffer, true);
    ArraySetAsSeries(atrBuffer, true);
    
    Print("EA initialized successfully!");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Print final statistics
    PrintStatistics();
    
    // Release indicator handles
    IndicatorRelease(handleEMA);
    IndicatorRelease(handleRSI);
    IndicatorRelease(handleMACD);
    IndicatorRelease(handleBB);
    IndicatorRelease(handleTrendEMA);
    IndicatorRelease(handleATR);
}

//+------------------------------------------------------------------+
//| Expert tick function                                               |
//+------------------------------------------------------------------+
void OnTick()
{
    // Update trade statistics on each tick
    UpdateStatistics();
    
    // Print statistics every 100 trades
    if(totalTrades > 0 && totalTrades % 100 == 0)
    {
        static int lastPrintedTrades = 0;
        if(totalTrades != lastPrintedTrades)
        {
            PrintStatistics();
            lastPrintedTrades = totalTrades;
        }
    }
    
    // Update indicator buffers
    if(CopyBuffer(handleEMA, 0, 0, 5, emaBuffer) <= 0) return;
    if(CopyBuffer(handleRSI, 0, 0, 5, rsiBuffer) <= 0) return;
    if(CopyBuffer(handleMACD, 0, 0, 5, macdMainBuffer) <= 0) return;
    if(CopyBuffer(handleMACD, 1, 0, 5, macdSignalBuffer) <= 0) return;
    if(CopyBuffer(handleBB, 0, 0, 5, bbMiddleBuffer) <= 0) return;
    if(CopyBuffer(handleBB, 1, 0, 5, bbUpperBuffer) <= 0) return;
    if(CopyBuffer(handleBB, 2, 0, 5, bbLowerBuffer) <= 0) return;
    if(CopyBuffer(handleTrendEMA, 0, 0, 5, trendEmaBuffer) <= 0) return;
    
    // Check if we already have positions
    if(PositionsTotal() > 0) return;
    
    // Check minimum time gap between trades
    if(TimeCurrent() - lastTradeTime < InpMinCandlesGap * PeriodSeconds(InpTimeframe)) return;
    
    // Enhanced trading logic
    bool buySignal = false;
    bool sellSignal = false;
    
    // Get current price for comparison
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Trend filter
    bool upTrend = true;
    bool downTrend = true;
    
    if(InpUseTrendFilter)
    {
        upTrend = (currentPrice > trendEmaBuffer[0] && trendEmaBuffer[0] > trendEmaBuffer[1]);
        downTrend = (currentPrice < trendEmaBuffer[0] && trendEmaBuffer[0] < trendEmaBuffer[1]);
    }
    
    // Enhanced Buy conditions (Multiple confirmations required)
    if(upTrend &&
       emaBuffer[0] > emaBuffer[1] &&                           // EMA rising
       emaBuffer[1] > emaBuffer[2] &&                           // EMA consistently rising
       rsiBuffer[0] > 40 && rsiBuffer[0] < 65 &&               // RSI in good range (not oversold/overbought)
       macdMainBuffer[0] > macdSignalBuffer[0] &&               // MACD bullish
       macdMainBuffer[0] > macdMainBuffer[1] &&                 // MACD rising
       currentPrice > emaBuffer[0] &&                           // Price above EMA
       currentPrice < bbUpperBuffer[0] &&                       // Not at BB upper (avoid buying at resistance)
       currentPrice > bbMiddleBuffer[0])                        // Above BB middle
    {
        // Additional momentum confirmation
        if(rsiBuffer[0] > rsiBuffer[1] || macdMainBuffer[0] > 0)
        {
            buySignal = true;
        }
    }
    
    // Enhanced Sell conditions (Multiple confirmations required)  
    if(downTrend &&
       emaBuffer[0] < emaBuffer[1] &&                           // EMA falling
       emaBuffer[1] < emaBuffer[2] &&                           // EMA consistently falling
       rsiBuffer[0] < 60 && rsiBuffer[0] > 35 &&               // RSI in good range
       macdMainBuffer[0] < macdSignalBuffer[0] &&               // MACD bearish
       macdMainBuffer[0] < macdMainBuffer[1] &&                 // MACD falling
       currentPrice < emaBuffer[0] &&                           // Price below EMA
       currentPrice > bbLowerBuffer[0] &&                       // Not at BB lower (avoid selling at support)
       currentPrice < bbMiddleBuffer[0])                        // Below BB middle
    {
        // Additional momentum confirmation
        if(rsiBuffer[0] < rsiBuffer[1] || macdMainBuffer[0] < 0)
        {
            sellSignal = true;
        }
    }
    
    // Execute trades with improved entry timing
    if(buySignal)
    {
        double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        trade(ORDER_TYPE_BUY, ask);
        lastTradeTime = TimeCurrent();
    }
    else if(sellSignal)
    {
        double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        trade(ORDER_TYPE_SELL, bid);
        lastTradeTime = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Enhanced Trading function                                          |
//+------------------------------------------------------------------+
void trade(ENUM_ORDER_TYPE orderType, double price)
{
    // Get symbol properties
    double points = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double minVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double volumeStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    long stopLevel = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
    
    // Ensure lot size is valid
    double volume = InpLotSize;
    if(volume < minVolume) volume = minVolume;
    if(volume > maxVolume) volume = maxVolume;
    volume = NormalizeDouble(MathFloor(volume / volumeStep) * volumeStep, 2);
    
    // Calculate SL and TP with risk-reward ratio
    double sl = 0, tp = 0;
    double minStopDistance = stopLevel * points;
    
    if(InpUseStops && stopLevel > 0)
    {
        // Use risk-reward ratio for better trade management
        double slDistance = MathMax(InpStopLoss * points, minStopDistance * 2);
        double tpDistance = slDistance * InpRiskReward; // Apply risk-reward ratio
        
        // Add spread buffer
        double spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * points;
        slDistance = MathMax(slDistance, (minStopDistance + spread) * 2);
        tpDistance = MathMax(tpDistance, (minStopDistance + spread) * 2);
        
        if(orderType == ORDER_TYPE_BUY)
        {
            sl = NormalizeDouble(price - slDistance, _Digits);
            tp = NormalizeDouble(price + tpDistance, _Digits);
        }
        else
        {
            sl = NormalizeDouble(price + slDistance, _Digits);
            tp = NormalizeDouble(price - tpDistance, _Digits);
        }
        
        // Final validation
        double actualSlDistance = MathAbs(price - sl);
        double actualTpDistance = MathAbs(price - tp);
        
        if(actualSlDistance < minStopDistance || actualTpDistance < minStopDistance)
        {
            sl = 0;
            tp = 0;
        }
    }
    
    // Prepare trade request
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = volume;
    request.type = orderType;
    request.price = NormalizeDouble(price, _Digits);
    request.sl = sl;
    request.tp = tp;
    request.deviation = 50;
    request.magic = 123456;
    request.comment = "Enhanced V75s EA";
    
    // Set filling type
    int filling_types = (int)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);
    if((filling_types & SYMBOL_FILLING_FOK) == SYMBOL_FILLING_FOK)
        request.type_filling = ORDER_FILLING_FOK;
    else if((filling_types & SYMBOL_FILLING_IOC) == SYMBOL_FILLING_IOC)
        request.type_filling = ORDER_FILLING_IOC;
    else
        request.type_filling = ORDER_FILLING_RETURN;
    
    // Send order
    bool success = OrderSend(request, result);
    
    if(!success)
    {
        int error = GetLastError();
        if((error == ERR_INVALID_STOPS || error == 4756) && (sl != 0 || tp != 0))
        {
            request.sl = 0;
            request.tp = 0;
            success = OrderSend(request, result);
        }
        
        if(!success)
        {
            Print("Trade failed, error: ", GetLastError());
        }
    }
    
    if(success)
    {
        totalTrades++;
        Print("Trade #", totalTrades, " opened: ", 
              (orderType == ORDER_TYPE_BUY ? "BUY" : "SELL"),
              " Volume: ", volume, " SL: ", sl, " TP: ", tp);
    }
}

//+------------------------------------------------------------------+
//| Update Statistics Function                                         |
//+------------------------------------------------------------------+
void UpdateStatistics()
{
    static int lastKnownTotalTrades = 0;
    
    // Count completed trades from history
    int historyTotal = HistoryDealsTotal();
    int newWins = 0, newLosses = 0;
    double newProfit = 0, newLoss = 0;
    
    // Check new deals since last update
    for(int i = 0; i < historyTotal; i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == 123456 && 
               HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                if(profit > 0)
                {
                    newWins++;
                    newProfit += profit;
                }
                else if(profit < 0)
                {
                    newLosses++;
                    newLoss += MathAbs(profit);
                }
            }
        }
    }
    
    // Update counters (simplified approach)
    if(newWins + newLosses > lastKnownTotalTrades)
    {
        winningTrades = newWins;
        losingTrades = newLosses;
        totalProfit = newProfit;
        totalLoss = newLoss;
        lastKnownTotalTrades = newWins + newLosses;
    }
}

//+------------------------------------------------------------------+
//| Print Statistics Function                                          |
//+------------------------------------------------------------------+
void PrintStatistics()
{
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double totalReturn = currentBalance - initialBalance;
    double winRate = (winningTrades + losingTrades > 0) ? 
                     (double)winningTrades / (winningTrades + losingTrades) * 100.0 : 0.0;
    
    Print("================================");
    Print("    ENHANCED V75s EA STATISTICS");
    Print("================================");
    Print("Total Trades Opened: ", totalTrades);
    Print("Completed Trades: ", (winningTrades + losingTrades));
    Print("Winning Trades: ", winningTrades);
    Print("Losing Trades: ", losingTrades);
    Print("Win Rate: ", NormalizeDouble(winRate, 2), "%");
    Print("Initial Balance: $", NormalizeDouble(initialBalance, 2));
    Print("Current Balance: $", NormalizeDouble(currentBalance, 2));
    Print("Total Return: $", NormalizeDouble(totalReturn, 2));
    if(initialBalance > 0)
        Print("Return %: ", NormalizeDouble(totalReturn/initialBalance*100, 2), "%");
    Print("Gross Profit: $", NormalizeDouble(totalProfit, 2));
    Print("Gross Loss: $", NormalizeDouble(totalLoss, 2));
    Print("================================");
}