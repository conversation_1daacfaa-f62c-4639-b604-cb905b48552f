{"cells": [{"cell_type": "markdown", "id": "3d3b0cb8", "metadata": {}, "source": ["# Reset Notebook\n", "\n", "To ensure a completely fresh start:\n", "1. First run the cell below to clear all variables\n", "2. Then click \"Runtime\" > \"Restart runtime\" in the menu\n", "3. After restart, you can begin with your new code"]}, {"cell_type": "code", "execution_count": null, "id": "677663b4", "metadata": {}, "outputs": [], "source": ["# Clear all variables from workspace\n", "%reset -f\n", "\n", "# Clear output from all cells\n", "from IPython.display import clear_output\n", "clear_output(wait=True)\n", "\n", "print(\"Workspace cleared. Please restart the runtime now (Runtime > Restart runtime)\")"]}, {"cell_type": "code", "execution_count": null, "id": "6b91fb86", "metadata": {}, "outputs": [], "source": ["class DerivDataCollector:\n", "    def __init__(self, url=WEBSOCKET_URL):\n", "        self.url = url\n", "        \n", "    async def connect(self):\n", "        self.ws = await websockets.connect(self.url)\n", "        \n", "    async def disconnect(self):\n", "        if hasattr(self, 'ws'):\n", "            await self.ws.close()\n", "            \n", "    async def get_candles(self, symbol, timeframe, start_time, end_time, count):\n", "        request = {\n", "            \"ticks_history\": symbol,\n", "            \"adjust_start_time\": 1,\n", "            \"count\": count,\n", "            \"end\": int(end_time.timestamp()),\n", "            \"start\": int(start_time.timestamp()),\n", "            \"style\": \"candles\",\n", "            \"granularity\": timeframe\n", "        }\n", "        \n", "        await self.ws.send(json.dumps(request))\n", "        response = await self.ws.recv()\n", "        return json.loads(response)\n", "    \n", "    def process_candles(self, data):\n", "        if 'error' in data:\n", "            raise Exception(f\"API Error: {data['error']['message']}\")\n", "            \n", "        candles = data['candles']\n", "        df = pd.DataFrame(candles)\n", "        df['timestamp'] = pd.to_datetime(df['epoch'], unit='s')\n", "        df = df.drop('epoch', axis=1)\n", "        return df.sort_values('timestamp')\n", "    \n", "    async def collect_historical_data(self, symbol, timeframe, start_date, chunk_size=CHUNK_SIZE):\n", "        current_date = datetime.now()\n", "        chunks = []\n", "        \n", "        with tqdm(total=(current_date - start_date).days) as pbar:\n", "            while start_date < current_date:\n", "                end_date = min(start_date + timed<PERSON>ta(days=1), current_date)\n", "                \n", "                for attempt in range(MAX_RETRIES):\n", "                    try:\n", "                        data = await self.get_candles(\n", "                            symbol=symbol,\n", "                            timeframe=timeframe,\n", "                            start_time=start_date,\n", "                            end_time=end_date,\n", "                            count=chunk_size\n", "                        )\n", "                        chunk_df = self.process_candles(data)\n", "                        chunks.append(chunk_df)\n", "                        break\n", "                    except Exception as e:\n", "                        if attempt == MAX_RETRIES - 1:\n", "                            print(f\"Failed to fetch data for {start_date}: {str(e)}\")\n", "                        else:\n", "                            await asyncio.sleep(RETRY_DELAY)\n", "                \n", "                start_date = end_date\n", "                pbar.update(1)\n", "        \n", "        if not chunks:\n", "            return pd.DataFrame()\n", "        \n", "        final_df = pd.concat(chunks, ignore_index=True)\n", "        final_df = final_df.drop_duplicates(subset=['timestamp'])\n", "        return final_df.sort_values('timestamp')"]}, {"cell_type": "code", "execution_count": null, "id": "30c32a40", "metadata": {}, "outputs": [], "source": ["async def collect_all_timeframes():\n", "    collector = DerivDataCollector()\n", "    await collector.connect()\n", "    \n", "    try:\n", "        for timeframe_name, granularity in TIMEFRAMES.items():\n", "            print(f\"\\nCollecting {timeframe_name} candles...\")\n", "            df = await collector.collect_historical_data(\n", "                symbol=SYMBOL,\n", "                timeframe=granularity,\n", "                start_date=START_DATE\n", "            )\n", "            \n", "            if not df.empty:\n", "                # Save to CSV\n", "                output_file = DATA_DIR / f'v75s_{timeframe_name}_data.csv'\n", "                df.to_csv(output_file, index=False)\n", "                print(f\"Saved {len(df)} candles to {output_file}\")\n", "            else:\n", "                print(f\"No data collected for {timeframe_name}\")\n", "                \n", "    finally:\n", "        await collector.disconnect()\n", "\n", "# Run the collection\n", "await collect_all_timeframes()"]}, {"cell_type": "code", "execution_count": null, "id": "c2fb5509", "metadata": {}, "outputs": [], "source": ["# Data Validation Functions\n", "def validate_timeframe_data(timeframe):\n", "    file_path = DATA_DIR / f'v75s_{timeframe}_data.csv'\n", "    if not file_path.exists():\n", "        print(f\"No data file found for {timeframe}\")\n", "        return\n", "    \n", "    df = pd.read_csv(file_path)\n", "    df['timestamp'] = pd.to_datetime(df['timestamp'])\n", "    \n", "    print(f\"\\nValidation Report for {timeframe}:\")\n", "    print(\"-\" * 50)\n", "    print(f\"Total records: {len(df)}\")\n", "    print(f\"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}\")\n", "    print(f\"Missing values: \\n{df.isnull().sum()}\")\n", "    \n", "    # Check for duplicate timestamps\n", "    duplicates = df[df.duplicated(subset=['timestamp'])]\n", "    print(f\"Duplicate timestamps: {len(duplicates)}\")\n", "    \n", "    # Basic statistics\n", "    print(\"\\nPrice statistics:\")\n", "    print(df[['open', 'high', 'low', 'close']].describe())\n", "    \n", "    return df\n", "\n", "# Validate all timeframes\n", "for timeframe in TIMEFRAMES.keys():\n", "    validate_timeframe_data(timeframe)"]}, {"cell_type": "markdown", "id": "58272692", "metadata": {}, "source": ["# V75s Data Collection and Analysis\n", "\n", "This notebook collects historical candlestick data for the Volatility 75 Index (V75s) from Deriv platform.\n", "\n", "## Step-by-Step Process:\n", "1. Setup and Dependencies\n", "2. Data Collection Configuration\n", "3. Historical Data Collection\n", "4. Data Validation and Storage"]}, {"cell_type": "markdown", "id": "958006ab", "metadata": {}, "source": ["## 1. Setup and Dependencies\n", "\n", "First, we'll install required packages and set up our workspace:"]}, {"cell_type": "code", "execution_count": null, "id": "14b73057", "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install websockets pandas numpy"]}, {"cell_type": "code", "execution_count": null, "id": "992dcd34", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import websockets\n", "import asyncio\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import os\n", "import time\n", "\n", "# Mount Google Drive for data storage\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Set up project directories\n", "PROJECT_PATH = '/content/drive/MyDrive/V75s_Trading'\n", "DATA_PATH = os.path.join(PROJECT_PATH, 'historical_data')\n", "\n", "# Create directories if they don't exist\n", "for path in [PROJECT_PATH, DATA_PATH]:\n", "    os.makedirs(path, exist_ok=True)\n", "    \n", "print(f\"Project directory: {PROJECT_PATH}\")\n", "print(f\"Data directory: {DATA_PATH}\")"]}, {"cell_type": "markdown", "id": "e8d27681", "metadata": {}, "source": ["# V75s Historical Data Collection\n", "\n", "This notebook collects historical candlestick data for the Volatility 75 Index (V75s) from Deriv platform.\n", "\n", "## Overview\n", "1. Setup Environment (Dependencies & Drive)\n", "2. WebSocket Connection Test\n", "3. Data Collection\n", "4. Data Validation\n", "\n", "Each section is clearly documented and organized for easy understanding."]}, {"cell_type": "markdown", "id": "ab9f6baf", "metadata": {}, "source": ["## 1. Setup Environment\n", "\n", "### 1.1 Install Required Packages"]}, {"cell_type": "code", "execution_count": null, "id": "e86dbb75", "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install websockets pandas numpy"]}, {"cell_type": "markdown", "id": "ab6361d7", "metadata": {}, "source": ["# V75s Data Collection\n", "\n", "This notebook focuses on collecting V75s candlestick data from Deriv platform.\n", "\n", "## Current Goal:\n", "- Set up connection\n", "- Collect candlestick data for all timeframes\n", "- Save data properly"]}, {"cell_type": "markdown", "id": "7c103ceb", "metadata": {}, "source": ["## 1. Initial Setup\n", "\n", "First, let's install necessary packages and set up our workspace:"]}, {"cell_type": "code", "execution_count": null, "id": "09aeec3b", "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install websockets pandas numpy"]}, {"cell_type": "code", "execution_count": null, "id": "5bc9fda0", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import websockets\n", "import asyncio\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import os\n", "import time\n", "\n", "# Mount Google Drive\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Set up project directories\n", "PROJECT_PATH = '/content/drive/MyDrive/V75s_Trading'\n", "DATA_PATH = os.path.join(PROJECT_PATH, 'historical_data')\n", "\n", "# Create directories if they don't exist\n", "for path in [PROJECT_PATH, DATA_PATH]:\n", "    os.makedirs(path, exist_ok=True)\n", "    \n", "print(f\"Project directory: {PROJECT_PATH}\")\n", "print(f\"Data directory: {DATA_PATH}\")"]}, {"cell_type": "markdown", "id": "22333b8e", "metadata": {}, "source": ["## 2. Test WebSocket Connection\n", "\n", "Let's first test our connection to Deriv's WebSocket server:"]}, {"cell_type": "code", "execution_count": null, "id": "8109d02b", "metadata": {}, "outputs": [], "source": ["async def test_connection():\n", "    \"\"\"Test connection to Deriv WebSocket server\"\"\"\n", "    uri = \"wss://ws.binaryws.com/websockets/v3?app_id=1089\"\n", "    \n", "    async with websockets.connect(uri) as websocket:\n", "        # First send a ping to test connection\n", "        ping_request = {\n", "            \"ping\": 1\n", "        }\n", "        await websocket.send(json.dumps(ping_request))\n", "        response = await websocket.recv()\n", "        print(\"Ping response:\", json.loads(response))\n", "        \n", "        # Test getting a single minute of data\n", "        current_time = int(time.time())\n", "        request = {\n", "            \"ticks_history\": \"1HZ75V\",\n", "            \"style\": \"candles\",\n", "            \"granularity\": 60,\n", "            \"end\": str(current_time),\n", "            \"start\": str(current_time - 120),\n", "            \"adjust_start_time\": 1\n", "        }\n", "        \n", "        await websocket.send(json.dumps(request))\n", "        response = await websocket.recv()\n", "        return json.loads(response)\n", "\n", "# Run the test\n", "test_response = await test_connection()\n", "print(\"\\nTest data request response:\")\n", "print(json.dumps(test_response, indent=2))"]}, {"cell_type": "markdown", "id": "8336544d", "metadata": {}, "source": ["## 3. Data Validation\n", "\n", "Let's check the collected data and show some statistics:"]}, {"cell_type": "code", "execution_count": null, "id": "437b8da4", "metadata": {}, "outputs": [], "source": ["# Load and check the collected data\n", "def validate_data():\n", "    csv_files = [f for f in os.listdir(DATA_PATH) if f.endswith('.csv')]\n", "    \n", "    for file in csv_files:\n", "        df = pd.read_csv(os.path.join(DATA_PATH, file))\n", "        print(f\"\\nValidating {file}:\")\n", "        print(f\"Total candles: {len(df)}\")\n", "        print(f\"Date range: {pd.to_datetime(df['datetime']).min()} to {pd.to_datetime(df['datetime']).max()}\")\n", "        print(f\"Missing values: {df.isnull().sum().sum()}\")\n", "        print(\"\\nSample data:\")\n", "        print(df.head())\n", "        \n", "validate_data()"]}, {"cell_type": "markdown", "id": "e2f844d9", "metadata": {}, "source": ["# V75s Trading Model Development\n", "\n", "This notebook implements a comprehensive trading model for the Volatility 75 Index (V75s) using historical data from 2024.\n", "\n", "## Structure:\n", "1. Setup & Dependencies\n", "2. Data Collection (All Timeframes)\n", "3. Data Processing & Technical Indicators\n", "4. Model Development\n", "5. Live Trading Implementation"]}, {"cell_type": "markdown", "id": "27aa5a78", "metadata": {}, "source": ["## 1. Setup & Dependencies\n", "\n", "Install required packages and set up Google Drive:"]}, {"cell_type": "code", "execution_count": null, "id": "8ac7ff0e", "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install websockets pandas numpy ta-lib\n", "\n", "# Import required libraries\n", "import websockets\n", "import asyncio\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import os\n", "import time\n", "import talib\n", "\n", "# Mount Google Drive\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Set up project directories\n", "PROJECT_PATH = '/content/drive/MyDrive/V75s_Trading'\n", "DATA_PATH = os.path.join(PROJECT_PATH, 'historical_data')\n", "\n", "# Create directories\n", "for path in [PROJECT_PATH, DATA_PATH]:\n", "    os.makedirs(path, exist_ok=True)\n", "    \n", "print(f\"Project directory: {PROJECT_PATH}\")\n", "print(f\"Data directory: {DATA_PATH}\")"]}, {"cell_type": "markdown", "id": "b791ea5a", "metadata": {}, "source": ["## 2. Data Collection\n", "\n", "Create data collector class for all timeframes (1m to 24h):"]}, {"cell_type": "code", "execution_count": null, "id": "8f1b1381", "metadata": {}, "outputs": [], "source": ["# Define V75s Data Collector Class\n", "class V75sDataCollector:\n", "    def __init__(self):\n", "        # Define all timeframes in seconds\n", "        self.timeframes = {\n", "            '1m': 60,\n", "            '2m': 120,\n", "            '3m': 180,\n", "            '5m': 300,\n", "            '10m': 600,\n", "            '15m': 900,\n", "            '30m': 1800,\n", "            '1h': 3600,\n", "            '2h': 7200,\n", "            '4h': 14400,\n", "            '8h': 28800,\n", "            '24h': 86400\n", "        }\n", "        self.uri = \"wss://ws.binaryws.com/websockets/v3?app_id=1089\"\n", "\n", "    async def get_candles(self, timeframe_seconds, start_time, end_time):\n", "        \"\"\"Get candles for a specific timeframe and time range\"\"\"\n", "        async with websockets.connect(self.uri) as websocket:\n", "            request = {\n", "                \"ticks_history\": \"1HZ75V\",\n", "                \"style\": \"candles\",\n", "                \"granularity\": timeframe_seconds,\n", "                \"start\": str(start_time),\n", "                \"end\": str(end_time),\n", "                \"adjust_start_time\": 1\n", "            }\n", "            \n", "            await websocket.send(json.dumps(request))\n", "            response = await websocket.recv()\n", "            return json.loads(response)\n", "\n", "    def process_candles(self, candles):\n", "        \"\"\"Convert candles to DataFrame and add datetime\"\"\"\n", "        df = pd.DataFrame(candles)\n", "        df['datetime'] = pd.to_datetime(df['epoch'], unit='s')\n", "        return df.sort_values('epoch').drop_duplicates(subset=['epoch'])\n", "\n", "    async def collect_timeframe(self, tf_name, tf_seconds):\n", "        \"\"\"Collect all data for one timeframe\"\"\"\n", "        print(f\"\\nCollecting {tf_name} timeframe...\")\n", "        \n", "        # Set time range (2024 to present)\n", "        end_time = int(time.time())\n", "        start_time = int(datetime(2024, 1, 1).timestamp())\n", "        all_candles = []\n", "        \n", "        # Collect data in chunks\n", "        current_start = start_time\n", "        while current_start < end_time:\n", "            current_end = min(current_start + (tf_seconds * 5000), end_time)\n", "            \n", "            print(f\"Getting {tf_name} from {datetime.fromtimestamp(current_start)} to {datetime.fromtimestamp(current_end)}\")\n", "            \n", "            response = await self.get_candles(tf_seconds, current_start, current_end)\n", "            \n", "            if 'error' in response:\n", "                print(f\"Error: {response['error']}\")\n", "                break\n", "                \n", "            if 'candles' in response:\n", "                all_candles.extend(response['candles'])\n", "                print(f\"Got {len(response['candles'])} candles\")\n", "            \n", "            current_start = current_end\n", "            await asyncio.sleep(1)  # Rate limiting\n", "        \n", "        if all_candles:\n", "            # Process and save data\n", "            df = self.process_candles(all_candles)\n", "            csv_path = os.path.join(DATA_PATH, f'v75s_{tf_name}_historical.csv')\n", "            df.to_csv(csv_path, index=False)\n", "            print(f\"Saved {len(df)} {tf_name} candles to {csv_path}\")\n", "            return df\n", "        \n", "        return None\n", "\n", "# Function to collect all timeframes\n", "async def collect_all_timeframes():\n", "    \"\"\"Collect data for all timeframes\"\"\"\n", "    collector = V75sDataCollector()\n", "    all_data = {}\n", "    \n", "    for tf_name, tf_seconds in collector.timeframes.items():\n", "        df = await collector.collect_timeframe(tf_name, tf_seconds)\n", "        if df is not None:\n", "            all_data[tf_name] = df\n", "    \n", "    # Print summary\n", "    print(\"\\n=== Data Collection Summary ===\")\n", "    for tf_name, df in all_data.items():\n", "        print(f\"\\n{tf_name} timeframe:\")\n", "        print(f\"Period: {df['datetime'].min()} to {df['datetime'].max()}\")\n", "        print(f\"Total candles: {len(df)}\")\n", "    \n", "    return all_data\n", "\n", "# Run the collection\n", "data = await collect_all_timeframes()"]}, {"cell_type": "markdown", "id": "f80bdbd5", "metadata": {}, "source": ["## 3. Data Processing & Technical Indicators\n", "\n", "After collecting data, we'll add technical indicators for model training:\n", "- Moving Averages (SMA, EMA)\n", "- RSI (Relative Strength Index)\n", "- MACD (Moving Average Convergence Divergence)\n", "- Bollinger Bands\n", "- Support/Resistance Levels\n", "- Volume Analysis"]}, {"cell_type": "markdown", "id": "d8cba83c", "metadata": {}, "source": ["# V75s Trading Model Development\n", "\n", "## Overview\n", "1. Setup and Dependencies\n", "2. Google Drive Connection\n", "3. Data Collection (All Timeframes)\n", "4. Data Processing and Technical Indicators\n", "5. Model Development"]}, {"cell_type": "markdown", "id": "a7e006b5", "metadata": {}, "source": ["## 1. Setup and Dependencies\n", "\n", "First, let's install and import all required packages:"]}, {"cell_type": "code", "execution_count": null, "id": "48f08e78", "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install websockets pandas numpy ta-lib"]}, {"cell_type": "code", "execution_count": null, "id": "7b946319", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import websockets\n", "import asyncio\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import os\n", "import time\n", "import talib"]}, {"cell_type": "markdown", "id": "2af4bf07", "metadata": {}, "source": ["## 2. Google Drive Connection\n", "\n", "Mount Google Drive and create project directories:"]}, {"cell_type": "code", "execution_count": null, "id": "2a777934", "metadata": {}, "outputs": [], "source": ["# Mount Google Drive\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Set up project directories\n", "PROJECT_PATH = '/content/drive/MyDrive/V75s_Trading'\n", "DATA_PATH = os.path.join(PROJECT_PATH, 'historical_data')\n", "\n", "# Create directories if they don't exist\n", "for path in [PROJECT_PATH, DATA_PATH]:\n", "    os.makedirs(path, exist_ok=True)\n", "    \n", "print(f\"Project directory: {PROJECT_PATH}\")\n", "print(f\"Data directory: {DATA_PATH}\")"]}, {"cell_type": "markdown", "id": "6176878d", "metadata": {}, "source": ["## 3. Data Collection (All Timeframes)\n", "\n", "We'll collect V75s data for all timeframes from 2024 to present:\n", "\n", "Timeframes:\n", "- Ultra-short: 1m, 2m, 3m\n", "- Short: 5m, 10m, 15m\n", "- Medium: 30m, 1h, 2h\n", "- Long: 4h, 8h, 24h\n", "\n", "First, let's create our data collector class:"]}, {"cell_type": "code", "execution_count": null, "id": "94e01d86", "metadata": {}, "outputs": [], "source": ["class V75sDataCollector:\n", "    def __init__(self):\n", "        # Define all timeframes in seconds\n", "        self.timeframes = {\n", "            '1m': 60,\n", "            '2m': 120,\n", "            '3m': 180,\n", "            '5m': 300,\n", "            '10m': 600,\n", "            '15m': 900,\n", "            '30m': 1800,\n", "            '1h': 3600,\n", "            '2h': 7200,\n", "            '4h': 14400,\n", "            '8h': 28800,\n", "            '24h': 86400\n", "        }\n", "        self.uri = \"wss://ws.binaryws.com/websockets/v3?app_id=1089\"\n", "\n", "    async def get_candles(self, timeframe_seconds, start_time, end_time):\n", "        \"\"\"Get candles for a specific timeframe and time range\"\"\"\n", "        async with websockets.connect(self.uri) as websocket:\n", "            request = {\n", "                \"ticks_history\": \"1HZ75V\",\n", "                \"style\": \"candles\",\n", "                \"granularity\": timeframe_seconds,\n", "                \"start\": str(start_time),\n", "                \"end\": str(end_time),\n", "                \"adjust_start_time\": 1\n", "            }\n", "            \n", "            await websocket.send(json.dumps(request))\n", "            response = await websocket.recv()\n", "            return json.loads(response)\n", "\n", "    def process_candles(self, candles):\n", "        \"\"\"Convert candles to DataFrame and add datetime\"\"\"\n", "        df = pd.DataFrame(candles)\n", "        df['datetime'] = pd.to_datetime(df['epoch'], unit='s')\n", "        return df.sort_values('epoch').drop_duplicates(subset=['epoch'])\n", "\n", "    async def collect_timeframe(self, tf_name, tf_seconds):\n", "        \"\"\"Collect all data for one timeframe\"\"\"\n", "        print(f\"\\nCollecting {tf_name} timeframe...\")\n", "        \n", "        # Set time range (2024 to present)\n", "        end_time = int(time.time())\n", "        start_time = int(datetime(2024, 1, 1).timestamp())\n", "        all_candles = []\n", "        \n", "        # Collect data in chunks to handle API limitations\n", "        current_start = start_time\n", "        while current_start < end_time:\n", "            current_end = min(current_start + (tf_seconds * 5000), end_time)\n", "            \n", "            print(f\"Getting {tf_name} from {datetime.fromtimestamp(current_start)} to {datetime.fromtimestamp(current_end)}\")\n", "            \n", "            response = await self.get_candles(tf_seconds, current_start, current_end)\n", "            \n", "            if 'error' in response:\n", "                print(f\"Error: {response['error']}\")\n", "                break\n", "                \n", "            if 'candles' in response:\n", "                all_candles.extend(response['candles'])\n", "                print(f\"Got {len(response['candles'])} candles\")\n", "            \n", "            current_start = current_end\n", "            await asyncio.sleep(1)  # Rate limiting\n", "        \n", "        if all_candles:\n", "            # Process and save data\n", "            df = self.process_candles(all_candles)\n", "            csv_path = os.path.join(DATA_PATH, f'v75s_{tf_name}_historical.csv')\n", "            df.to_csv(csv_path, index=False)\n", "            print(f\"Saved {len(df)} {tf_name} candles to {csv_path}\")\n", "            return df\n", "        \n", "        return None"]}, {"cell_type": "code", "execution_count": null, "id": "e27d4db0", "metadata": {}, "outputs": [], "source": ["async def collect_all_timeframes():\n", "    \"\"\"Collect data for all timeframes\"\"\"\n", "    collector = V75sDataCollector()\n", "    all_data = {}\n", "    \n", "    for tf_name, tf_seconds in collector.timeframes.items():\n", "        df = await collector.collect_timeframe(tf_name, tf_seconds)\n", "        if df is not None:\n", "            all_data[tf_name] = df\n", "    \n", "    # Print summary\n", "    print(\"\\n=== Data Collection Summary ===\")\n", "    for tf_name, df in all_data.items():\n", "        print(f\"\\n{tf_name} timeframe:\")\n", "        print(f\"Period: {df['datetime'].min()} to {df['datetime'].max()}\")\n", "        print(f\"Total candles: {len(df)}\")\n", "    \n", "    return all_data\n", "\n", "# Run the collection\n", "data = await collect_all_timeframes()"]}, {"cell_type": "markdown", "id": "7693d56c", "metadata": {}, "source": ["# V75s Trading Model Development\n", "\n", "This notebook implements a trading model for the Volatility 75 Index (V75s) using Google Colab. We'll use:\n", "- Google Drive for data storage\n", "- WebSocket connection for data collection\n", "- Deep learning for price prediction\n", "\n", "## Setup Steps:\n", "1. Mount Google Drive\n", "2. Install required packages\n", "3. Set up WebSocket connection\n", "4. Collect and store data\n", "5. Prepare for model development"]}, {"cell_type": "markdown", "id": "12ddf79b", "metadata": {}, "source": ["## 1. Mount Google Drive\n", "\n", "First, we'll mount Google Drive to store our collected data and models:"]}, {"cell_type": "code", "execution_count": null, "id": "a5cd70f0", "metadata": {}, "outputs": [], "source": ["# Mount Google Drive\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Create project directory\n", "import os\n", "\n", "# Define project path\n", "PROJECT_PATH = '/content/drive/MyDrive/V75s_Trading'\n", "\n", "# Create project directory if it doesn't exist\n", "if not os.path.exists(PROJECT_PATH):\n", "    os.makedirs(PROJECT_PATH)\n", "    os.makedirs(os.path.join(PROJECT_PATH, 'data'))\n", "    os.makedirs(os.path.join(PROJECT_PATH, 'models'))\n", "    print(f\"Created project directories at {PROJECT_PATH}\")\n", "else:\n", "    print(f\"Project directory already exists at {PROJECT_PATH}\")"]}, {"cell_type": "markdown", "id": "2781eb6b", "metadata": {}, "source": ["## 2. Install Required Packages\n", "\n", "Install necessary packages for data collection and processing:"]}, {"cell_type": "code", "execution_count": null, "id": "419fecab", "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install websockets pandas numpy tensorflow"]}, {"cell_type": "markdown", "id": "e436425b", "metadata": {}, "source": ["## 3. WebSocket Connection Setup\n", "\n", "Test connection to Deriv's WebSocket server and collect initial data:"]}, {"cell_type": "markdown", "id": "e5fbf9f1", "metadata": {}, "source": ["# Multi-timeframe Data Collection\n", "\n", "We'll collect data for multiple timeframes:\n", "- 1 minute (60 seconds)\n", "- 5 minutes (300 seconds)\n", "- 15 minutes (900 seconds)\n", "- 1 hour (3600 seconds)\n", "\n", "And calculate technical indicators:\n", "- Moving Averages (SMA, EMA)\n", "- RSI (Relative Strength Index)\n", "- MACD (Moving Average Convergence Divergence)\n", "- Bollinger Bands"]}, {"cell_type": "markdown", "id": "3dd94da6", "metadata": {}, "source": ["# Historical Data Collection (2024-Present)\n", "\n", "We'll collect data for all timeframes from 2024 to present:\n", "- 1 minute (60s)\n", "- 2 minutes (120s)\n", "- 3 minutes (180s)\n", "- 5 minutes (300s)\n", "- 10 minutes (600s)\n", "- 15 minutes (900s)\n", "- 30 minutes (1800s)\n", "- 1 hour (3600s)\n", "- 2 hours (7200s)\n", "- 4 hours (14400s)\n", "- 8 hours (28800s)\n", "- 24 hours (86400s)\n", "\n", "This comprehensive dataset will help us capture:\n", "1. <PERSON><PERSON><PERSON> opportunities (1m-5m)\n", "2. Short-term trades (15m-1h)\n", "3. Swing trades (4h-24h)\n", "4. Long-term trends (Daily)"]}, {"cell_type": "code", "execution_count": null, "id": "b09e26d9", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import websockets\n", "import asyncio\n", "import json\n", "import os\n", "from datetime import datetime, timedelta\n", "import time\n", "\n", "class V75sHistoricalCollector:\n", "    def __init__(self):\n", "        self.timeframes = {\n", "            '1m': 60,\n", "            '2m': 120,\n", "            '3m': 180,\n", "            '5m': 300,\n", "            '10m': 600,\n", "            '15m': 900,\n", "            '30m': 1800,\n", "            '1h': 3600,\n", "            '2h': 7200,\n", "            '4h': 14400,\n", "            '8h': 28800,\n", "            '24h': 86400\n", "        }\n", "        self.uri = \"wss://ws.binaryws.com/websockets/v3?app_id=1089\"\n", "        \n", "        # Create data directory if it doesn't exist\n", "        self.data_dir = os.path.join(PROJECT_PATH, 'historical_data')\n", "        os.makedirs(self.data_dir, exist_ok=True)\n", "\n", "    async def get_historical_data(self, timeframe_seconds, start_time, end_time):\n", "        async with websockets.connect(self.uri) as websocket:\n", "            request = {\n", "                \"ticks_history\": \"1HZ75V\",\n", "                \"style\": \"candles\",\n", "                \"granularity\": timeframe_seconds,\n", "                \"start\": str(start_time),\n", "                \"end\": str(end_time),\n", "                \"adjust_start_time\": 1\n", "            }\n", "            \n", "            await websocket.send(json.dumps(request))\n", "            response = await websocket.recv()\n", "            return json.loads(response)\n", "\n", "    async def collect_timeframe_data(self, tf_name, tf_seconds):\n", "        print(f\"\\nCollecting {tf_name} timeframe data...\")\n", "        \n", "        # Calculate timestamps\n", "        end_time = int(time.time())\n", "        start_time = int(datetime(2024, 1, 1).timestamp())\n", "        \n", "        all_candles = []\n", "        current_start = start_time\n", "        \n", "        while current_start < end_time:\n", "            current_end = min(current_start + (tf_seconds * 5000), end_time)\n", "            \n", "            print(f\"Fetching {tf_name} data from {datetime.fromtimestamp(current_start)} to {datetime.fromtimestamp(current_end)}\")\n", "            \n", "            response = await self.get_historical_data(tf_seconds, current_start, current_end)\n", "            \n", "            if 'error' in response:\n", "                print(f\"Error collecting {tf_name} data:\", response['error'])\n", "                break\n", "                \n", "            if 'candles' in response:\n", "                all_candles.extend(response['candles'])\n", "                print(f\"Received {len(response['candles'])} candles\")\n", "            \n", "            current_start = current_end\n", "            await asyncio.sleep(1)  # Rate limiting\n", "        \n", "        if all_candles:\n", "            df = pd.DataFrame(all_candles)\n", "            df['datetime'] = pd.to_datetime(df['epoch'], unit='s')\n", "            \n", "            # Sort by timestamp and remove duplicates\n", "            df = df.sort_values('epoch').drop_duplicates(subset=['epoch'])\n", "            \n", "            # Save to CSV\n", "            csv_path = os.path.join(self.data_dir, f'v75s_{tf_name}_historical.csv')\n", "            df.to_csv(csv_path, index=False)\n", "            print(f\"\\nSaved {len(df)} {tf_name} candles to {csv_path}\")\n", "            \n", "            return df\n", "        \n", "        return None\n", "\n", "    async def collect_all_historical_data(self):\n", "        all_data = {}\n", "        \n", "        for tf_name, tf_seconds in self.timeframes.items():\n", "            df = await self.collect_timeframe_data(tf_name, tf_seconds)\n", "            if df is not None:\n", "                all_data[tf_name] = df\n", "        \n", "        return all_data\n", "\n", "# Create collector and start historical data collection\n", "collector = V75sHistoricalCollector()\n", "historical_data = await collector.collect_all_historical_data()\n", "\n", "# Print summary of collected data\n", "print(\"\\nData Collection Summary:\")\n", "for tf_name, df in historical_data.items():\n", "    print(f\"\\n{tf_name} timeframe:\")\n", "    print(f\"Period: {df['datetime'].min()} to {df['datetime'].max()}\")\n", "    print(f\"Total candles: {len(df)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "5ac2e90a", "metadata": {}, "outputs": [], "source": ["# Install required technical analysis library\n", "!pip install ta-lib\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import talib\n", "from datetime import datetime, timedelta\n", "import time\n", "\n", "class V75sDataCollector:\n", "    def __init__(self):\n", "        self.timeframes = {\n", "            '1m': 60,\n", "            '5m': 300,\n", "            '15m': 900,\n", "            '1h': 3600\n", "        }\n", "        self.uri = \"wss://ws.binaryws.com/websockets/v3?app_id=1089\"\n", "    \n", "    async def get_candles(self, timeframe_seconds, count=100):\n", "        async with websockets.connect(self.uri) as websocket:\n", "            current_time = int(time.time())\n", "            request = {\n", "                \"ticks_history\": \"1HZ75V\",\n", "                \"style\": \"candles\",\n", "                \"granularity\": timeframe_seconds,\n", "                \"end\": str(current_time),\n", "                \"start\": str(current_time - (timeframe_seconds * count)),\n", "                \"adjust_start_time\": 1\n", "            }\n", "            \n", "            await websocket.send(json.dumps(request))\n", "            response = await websocket.recv()\n", "            return json.loads(response)\n", "    \n", "    def add_technical_indicators(self, df):\n", "        # Convert to numpy arrays for TA-Lib\n", "        close = df['close'].values\n", "        high = df['high'].values\n", "        low = df['low'].values\n", "        \n", "        # Add Moving Averages\n", "        df['SMA_20'] = talib.SMA(close, timeperiod=20)\n", "        df['EMA_20'] = talib.EMA(close, timeperiod=20)\n", "        \n", "        # Add RSI\n", "        df['RSI_14'] = talib.RSI(close, timeperiod=14)\n", "        \n", "        # Add MACD\n", "        macd, macd_signal, macd_hist = talib.MACD(close)\n", "        df['MACD'] = macd\n", "        df['MACD_Signal'] = macd_signal\n", "        df['MACD_Hist'] = macd_hist\n", "        \n", "        # Add Bollinger Bands\n", "        upper, middle, lower = talib.BBANDS(close, timeperiod=20)\n", "        df['BB_Upper'] = upper\n", "        df['BB_Middle'] = middle\n", "        df['BB_Lower'] = lower\n", "        \n", "        return df\n", "\n", "    async def collect_all_timeframes(self):\n", "        all_data = {}\n", "        \n", "        for tf_name, tf_seconds in self.timeframes.items():\n", "            print(f\"Collecting {tf_name} timeframe data...\")\n", "            response = await self.get_candles(tf_seconds)\n", "            \n", "            if 'candles' in response:\n", "                df = pd.DataFrame(response['candles'])\n", "                df['datetime'] = pd.to_datetime(df['epoch'], unit='s')\n", "                df = self.add_technical_indicators(df)\n", "                all_data[tf_name] = df\n", "                \n", "                # Save to CSV\n", "                csv_path = os.path.join(PROJECT_PATH, f'data/v75s_{tf_name}.csv')\n", "                df.to_csv(csv_path, index=False)\n", "                print(f\"Saved {tf_name} data to {csv_path}\")\n", "            else:\n", "                print(f\"Error collecting {tf_name} data:\", response.get('error', 'Unknown error'))\n", "        \n", "        return all_data\n", "\n", "# Create collector and get data\n", "collector = V75sDataCollector()\n", "await collector.collect_all_timeframes()"]}, {"cell_type": "code", "execution_count": null, "id": "71474421", "metadata": {}, "outputs": [], "source": ["import websockets\n", "import asyncio\n", "import json\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "import os\n", "import time\n", "\n", "async def test_deriv_connection():\n", "    uri = \"wss://ws.binaryws.com/websockets/v3?app_id=1089\"\n", "    \n", "    async with websockets.connect(uri) as websocket:\n", "        # First send a ping to establish connection\n", "        ping_request = {\n", "            \"ping\": 1\n", "        }\n", "        await websocket.send(json.dumps(ping_request))\n", "        ping_response = await websocket.recv()\n", "        print(\"Ping response:\", json.loads(ping_response))\n", "        \n", "        # Request V75s (1HZ75V) candlesticks with explicit timestamps\n", "        current_time = int(time.time())\n", "        request = {\n", "            \"ticks_history\": \"1HZ75V\",\n", "            \"style\": \"candles\",\n", "            \"granularity\": 60,\n", "            \"end\": str(current_time),\n", "            \"start\": str(current_time - 6000),  # Last 100 minutes\n", "            \"subscribe\": 0,\n", "            \"adjust_start_time\": 1\n", "        }\n", "        \n", "        print(\"Sending request:\", json.dumps(request, indent=2))\n", "        await websocket.send(json.dumps(request))\n", "        response = await websocket.recv()\n", "        return json.loads(response)\n", "\n", "async def main():\n", "    try:\n", "        response = await test_deriv_connection()\n", "        print(\"\\nResponse received!\")\n", "        print(\"\\nResponse structure:\")\n", "        print(json.dumps(response, indent=2))\n", "        \n", "        # If we got candles data, convert to DataFrame\n", "        if 'candles' in response:\n", "            candles = pd.DataFrame(response['candles'])\n", "            print(\"\\nFirst few candles:\")\n", "            print(candles.head())\n", "            \n", "            # Save candles to CSV if we have data\n", "            csv_path = os.path.join(PROJECT_PATH, 'data/v75s_candles.csv')\n", "            candles.to_csv(csv_path, index=False)\n", "            print(f\"\\nSaved candles data to {csv_path}\")\n", "        \n", "        # Save raw response\n", "        with open(os.path.join(PROJECT_PATH, 'data/sample_response.json'), 'w') as f:\n", "            json.dump(response, f, indent=2)\n", "            print(f\"Saved raw response to {os.path.join(PROJECT_PATH, 'data/sample_response.json')}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"Error: {str(e)}\")\n", "\n", "# Execute the test\n", "await main()"]}, {"cell_type": "code", "execution_count": null, "id": "4c786bb9", "metadata": {}, "outputs": [], "source": ["import websockets\n", "import asyncio\n", "import json\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "import os\n", "import time\n", "\n", "async def test_deriv_connection():\n", "    # Using demo app_id\n", "    uri = \"wss://ws.binaryws.com/websockets/v3?app_id=1089\"\n", "    \n", "    async with websockets.connect(uri) as websocket:\n", "        # First send a ping to establish connection\n", "        ping_request = {\n", "            \"ping\": 1\n", "        }\n", "        await websocket.send(json.dumps(ping_request))\n", "        ping_response = await websocket.recv()\n", "        print(\"Ping response:\", json.loads(ping_response))\n", "        \n", "        # Calculate timestamps\n", "        end_time = int(time.time())  # Current time in epochs\n", "        start_time = end_time - (60 * 100)  # 100 minutes ago\n", "        \n", "        # Request V75s (1HZ75V) candlesticks\n", "        request = {\n", "            \"ticks_history\": \"1HZ75V\",\n", "            \"style\": \"candles\",\n", "            \"granularity\": 60,  # 1-minute candles\n", "            \"count\": 100,       # Last 100 candles\n", "            \"adjust_start_time\": 1,\n", "            \"end\": end_time,\n", "            \"start\": start_time\n", "        }\n", "        \n", "        await websocket.send(json.dumps(request))\n", "        response = await websocket.recv()\n", "        return json.loads(response)\n", "\n", "# Run the test\n", "async def main():\n", "    try:\n", "        response = await test_deriv_connection()\n", "        print(\"Connection successful!\")\n", "        print(\"\\nResponse structure:\")\n", "        print(json.dumps(response, indent=2))\n", "        \n", "        # If we got candles data, convert to DataFrame\n", "        if 'candles' in response:\n", "            candles = pd.DataFrame(response['candles'])\n", "            print(\"\\nFirst few candles:\")\n", "            print(candles.head())\n", "        \n", "        # Save response to file for analysis\n", "        with open(os.path.join(PROJECT_PATH, 'data/sample_response.json'), 'w') as f:\n", "            json.dump(response, f, indent=2)\n", "            print(f\"\\nSaved response to {os.path.join(PROJECT_PATH, 'data/sample_response.json')}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"Error: {str(e)}\")\n", "\n", "# Execute the test\n", "await main()"]}, {"cell_type": "code", "execution_count": null, "id": "d7e5ee7d", "metadata": {}, "outputs": [], "source": ["import websockets\n", "import asyncio\n", "import json\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "import os\n", "\n", "async def test_deriv_connection():\n", "    # Using demo app_id\n", "    uri = \"wss://ws.binaryws.com/websockets/v3?app_id=1089\"\n", "    \n", "    async with websockets.connect(uri) as websocket:\n", "        # First send a ping to establish connection\n", "        ping_request = {\n", "            \"ping\": 1\n", "        }\n", "        await websocket.send(json.dumps(ping_request))\n", "        ping_response = await websocket.recv()\n", "        print(\"Ping response:\", json.loads(ping_response))\n", "        \n", "        # Request V75s (1HZ75V) candlesticks\n", "        request = {\n", "            \"ticks_history\": \"1HZ75V\",\n", "            \"style\": \"candles\",\n", "            \"granularity\": 60,  # 1-minute candles\n", "            \"count\": 100,       # Last 100 candles\n", "            \"adjust_start_time\": 1\n", "        }\n", "        \n", "        await websocket.send(json.dumps(request))\n", "        response = await websocket.recv()\n", "        return json.loads(response)\n", "\n", "# Run the test\n", "async def main():\n", "    try:\n", "        response = await test_deriv_connection()\n", "        print(\"Connection successful!\")\n", "        print(\"\\nResponse structure:\")\n", "        print(json.dumps(response, indent=2))\n", "        \n", "        # Save response to file for analysis\n", "        with open(os.path.join(PROJECT_PATH, 'data/sample_response.json'), 'w') as f:\n", "            json.dump(response, f, indent=2)\n", "            print(f\"\\nSaved response to {os.path.join(PROJECT_PATH, 'data/sample_response.json')}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"Error: {str(e)}\")\n", "\n", "# Execute the test\n", "await main()"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}