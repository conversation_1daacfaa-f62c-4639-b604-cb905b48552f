//+------------------------------------------------------------------+
//|                                                       V75s_EA.mq5 |
//|                                       Copyright 2024, Your Name   |
//|                                             https://www.mql5.com/ |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

// Error code constants
#define ERR_INVALID_STOPS 130
#define TRADE_RETCODE_INVALID_STOPS 4756

// Input Parameters
input ENUM_TIMEFRAMES InpTimeframe = PERIOD_M1;  // Trading Timeframe
input int    InpEMAPeriod     = 20;      // EMA Period
input int    InpRSIPeriod     = 14;      // RSI Period
input int    InpMACDFast      = 12;      // MACD Fast Period
input int    InpMACDSlow      = 26;      // MACD Slow Period
input int    InpMACDSignal    = 9;       // MACD Signal Period
input double InpLotSize       = 0.01;    // Lot Size
input int    InpStopLoss      = 100;     // Stop Loss (points)
input int    InpTakeProfit    = 200;     // Take Profit (points)

// Global Variables
int    handleEMA;                        // EMA indicator handle
int    handleRSI;                        // RSI indicator handle
int    handleMACD;                       // MACD indicator handle
double emaBuffer[];                      // EMA values buffer
double rsiBuffer[];                      // RSI values buffer
double macdMainBuffer[];                 // MACD main line buffer
double macdSignalBuffer[];               // MACD signal line buffer

//+------------------------------------------------------------------+
//| Expert initialization function                                     |
//+------------------------------------------------------------------+
int OnInit()
{
    // Symbol verification is no longer needed as we accept all symbols
    // Just log the symbol being used for reference
    Print("EA attached to symbol: ", _Symbol);
    
    // Initialize indicator handles
    handleEMA = iMA(_Symbol, InpTimeframe, InpEMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
    handleRSI = iRSI(_Symbol, InpTimeframe, InpRSIPeriod, PRICE_CLOSE);
    handleMACD = iMACD(_Symbol, InpTimeframe, InpMACDFast, InpMACDSlow, InpMACDSignal, PRICE_CLOSE);
    
    // Validate indicator handles
    if(handleEMA == INVALID_HANDLE || handleRSI == INVALID_HANDLE || handleMACD == INVALID_HANDLE)
    {
        Print("Error initializing indicators!");
        return INIT_FAILED;
    }
    
    // Initialize arrays
    ArraySetAsSeries(emaBuffer, true);
    ArraySetAsSeries(rsiBuffer, true);
    ArraySetAsSeries(macdMainBuffer, true);
    ArraySetAsSeries(macdSignalBuffer, true);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    IndicatorRelease(handleEMA);
    IndicatorRelease(handleRSI);
    IndicatorRelease(handleMACD);
}

//+------------------------------------------------------------------+
//| Expert tick function                                               |
//+------------------------------------------------------------------+
void OnTick()
{
    // Update indicator buffers
    if(CopyBuffer(handleEMA, 0, 0, 3, emaBuffer) <= 0) return;
    if(CopyBuffer(handleRSI, 0, 0, 3, rsiBuffer) <= 0) return;
    if(CopyBuffer(handleMACD, 0, 0, 3, macdMainBuffer) <= 0) return;
    if(CopyBuffer(handleMACD, 1, 0, 3, macdSignalBuffer) <= 0) return;
    
    // Check if we already have positions
    if(PositionsTotal() > 0) return;
    
    // Trading logic
    bool buySignal = false;
    bool sellSignal = false;
    
    // Buy conditions
    if(emaBuffer[0] > emaBuffer[1] &&           // EMA trending up
       rsiBuffer[0] < 70 &&                     // RSI not overbought
       macdMainBuffer[0] > macdSignalBuffer[0]) // MACD crossover
    {
        buySignal = true;
    }
    
    // Sell conditions
    if(emaBuffer[0] < emaBuffer[1] &&           // EMA trending down
       rsiBuffer[0] > 30 &&                     // RSI not oversold
       macdMainBuffer[0] < macdSignalBuffer[0]) // MACD crossunder
    {
        sellSignal = true;
    }
    
    // Execute trades
    if(buySignal)
    {
        double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        trade(ORDER_TYPE_BUY, ask);
    }
    else if(sellSignal)
    {
        double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        trade(ORDER_TYPE_SELL, bid);
    }
}

//+------------------------------------------------------------------+
//| Trading function                                                   |
//+------------------------------------------------------------------+
void trade(ENUM_ORDER_TYPE orderType, double price)
{
    // Get symbol properties
    double points = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double minVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double volumeStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    // Ensure lot size is valid
    double volume = InpLotSize;
    if(volume < minVolume) volume = minVolume;
    if(volume > maxVolume) volume = maxVolume;
    
    // Normalize volume to step size
    volume = NormalizeDouble(MathFloor(volume / volumeStep) * volumeStep, 2);
    
    // Calculate SL and TP
    double sl = 0, tp = 0;
    
    // Get minimum stop level in points
    long stopLevel = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
    double minStopDistPoints = stopLevel * points;
    
    // For Volatility 75 Index and similar synthetic indices, often stops are not allowed
    // or have very specific requirements. Let's start without stops to avoid issues.
    bool useStops = false;
    
    // Only try to use stops if stop level is reasonable (less than 100 points)
    if(stopLevel > 0 && stopLevel < 100)
    {
        useStops = true;
        Print("Stop level acceptable (", stopLevel, " points), will use stops");
    }
    else
    {
        Print("Stop level problematic (", stopLevel, " points) or stops not supported, trading without stops");
    }
    
    if(useStops)
    {
        // Ensure stop loss and take profit are at least at minimum distance
        double actualStopLoss = MathMax(InpStopLoss * points, minStopDistPoints);
        double actualTakeProfit = MathMax(InpTakeProfit * points, minStopDistPoints);
        
        if(orderType == ORDER_TYPE_BUY)
        {
            sl = NormalizeDouble(price - actualStopLoss, _Digits);
            tp = NormalizeDouble(price + actualTakeProfit, _Digits);
        }
        else
        {
            sl = NormalizeDouble(price + actualStopLoss, _Digits);
            tp = NormalizeDouble(price - actualTakeProfit, _Digits);
        }
        
        // Print stop level information for debugging
        Print("Using stops - Stop level: ", stopLevel, " points, Min distance: ", minStopDistPoints,
              ", Actual SL distance: ", actualStopLoss / points, " points",
              ", Actual TP distance: ", actualTakeProfit / points, " points");
    }
    else
    {
        // Don't use stops
        sl = 0;
        tp = 0;
        Print("Trading without stops");
    }

    
    // Prepare trade request
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = volume;
    request.type = orderType;
    request.price = NormalizeDouble(price, _Digits);
    request.sl = sl;
    request.tp = tp;
    request.deviation = 10;
    request.magic = 123456; // Magic number for identifying EA trades
    request.comment = "V75s EA";
    
    // Try different filling types if needed
    int filling_types = (int)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);
    if((filling_types & SYMBOL_FILLING_FOK) == SYMBOL_FILLING_FOK)
        request.type_filling = ORDER_FILLING_FOK;
    else if((filling_types & SYMBOL_FILLING_IOC) == SYMBOL_FILLING_IOC)
        request.type_filling = ORDER_FILLING_IOC;
    else
        request.type_filling = ORDER_FILLING_RETURN;
    
    // Send order
    bool success = OrderSend(request, result);
    
    // Log detailed information
    if(!success)
    {
        int error = GetLastError();
        Print("Error opening position: ", error, 
              ", Symbol: ", _Symbol,
              ", Volume: ", volume,
              ", Min Volume: ", minVolume,
              ", Volume Step: ", volumeStep);
        
        // If error is related to invalid stops, try again without stops
        if((error == ERR_INVALID_STOPS || error == TRADE_RETCODE_INVALID_STOPS) && (sl != 0 || tp != 0))
        {
            Print("Retrying order without stops due to invalid stops error: ", error);
            request.sl = 0;
            request.tp = 0;
            
            if(!OrderSend(request, result))
            {
                error = GetLastError();
                Print("Error opening position without stops: ", error, ", Symbol: ", _Symbol);
            }
            else
            {
                Print("Order placed successfully without stops: ", result.order);
                success = true;
            }
        }
    }
    else
    {
        Print("Order placed successfully: ", result.order, ", Volume: ", volume);
    }
}