{"cells": [{"cell_type": "code", "execution_count": null, "id": "3ed77e4c", "metadata": {}, "outputs": [], "source": ["# Test Cell - Run this first to verify output is working\n", "print(\"Testing notebook output...\")\n", "print(\"If you can see this message, output is working correctly.\")\n", "print(\"Now you can run the training cell below.\")"]}, {"cell_type": "code", "execution_count": null, "id": "504d90aa", "metadata": {}, "outputs": [], "source": ["# Cell 1: Setup and Imports\n", "# Install required packages\n", "!pip install ta pandas numpy scikit-learn tensorflow -q\n", "\n", "# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from ta.trend import SMAIndicator, EMAIndicator, MACD\n", "from ta.momentum import RSIIndicator, StochRSIIndicator\n", "from ta.volatility import BollingerBands, AverageTrueRange\n", "from sklearn.preprocessing import StandardScaler\n", "import gc  # For garbage collection\n", "from datetime import datetime, timedelta\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up project directories\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Define project directories\n", "PROJECT_DIR = Path('/content/drive/MyDrive/V75s_Trading')\n", "DATA_DIR = PROJECT_DIR / 'historical_data'\n", "SAVE_DIR = PROJECT_DIR / 'preprocessed_data'\n", "\n", "# Create directories if they don't exist\n", "PROJECT_DIR.mkdir(exist_ok=True)\n", "DATA_DIR.mkdir(exist_ok=True)\n", "SAVE_DIR.mkdir(exist_ok=True)\n", "\n", "print(f\"Project directory: {PROJECT_DIR}\")\n", "print(f\"Data directory: {DATA_DIR}\")\n", "print(f\"Save directory: {SAVE_DIR}\")\n", "\n", "# Verify data files exist\n", "print(\"\\nChecking data files...\")\n", "timeframes = ['1m', '5m', '30m', '1h', '8h']\n", "missing_files = []\n", "\n", "for tf in timeframes:\n", "    file_path = DATA_DIR / f'v75s_{tf}_historical.csv'\n", "    if not file_path.exists():\n", "        missing_files.append(f'v75s_{tf}_historical.csv')\n", "\n", "if missing_files:\n", "    print(\"Error: The following data files are missing:\")\n", "    for file in missing_files:\n", "        print(f\"- {file}\")\n", "    print(f\"\\nPlease ensure all data files are present in: {DATA_DIR}\")\n", "    raise FileNotFoundError(\"Missing data files\")\n", "\n", "print(\"All data files found. Starting preprocessing...\")"]}, {"cell_type": "code", "execution_count": null, "id": "a579deff", "metadata": {}, "outputs": [], "source": ["# Cell 2: Feature Engineering Functions\n", "\n", "def add_technical_indicators(df):\n", "    \"\"\"Add essential technical indicators with memory optimization\"\"\"\n", "    print(\"Adding technical indicators...\")\n", "    \n", "    # Core moving averages\n", "    df['sma_20'] = SMAIndicator(close=df['close'], window=20).sma_indicator()\n", "    df['sma_50'] = SMAIndicator(close=df['close'], window=50).sma_indicator()\n", "    df['ema_20'] = EMAIndicator(close=df['close'], window=20).ema_indicator()\n", "    \n", "    # MACD\n", "    macd = MACD(close=df['close'])\n", "    df['macd'] = macd.macd()\n", "    df['macd_signal'] = macd.macd_signal()\n", "    \n", "    # RSI and Stochastic\n", "    df['rsi'] = RSIIndicator(close=df['close']).rsi()\n", "    df['stoch_rsi_k'] = StochRSIIndicator(close=df['close']).stochrsi_k()\n", "    \n", "    # Bollinger and ATR\n", "    bb = BollingerBands(close=df['close'])\n", "    df['bb_high'] = bb.bollinger_hband()\n", "    df['bb_low'] = bb.bollinger_lband()\n", "    df['atr'] = AverageTrueRange(high=df['high'], low=df['low'], close=df['close']).average_true_range()\n", "    \n", "    gc.collect()\n", "    return df\n", "\n", "def calculate_market_regime(df):\n", "    \"\"\"Simplified market regime analysis\"\"\"\n", "    df['volatility'] = (df['high'] - df['low']) / df['close']\n", "    df['trend_strength'] = abs(df['close'] - df['sma_20']) / df['close']\n", "    return df\n", "\n", "def calculate_candle_patterns(df):\n", "    \"\"\"Basic candlestick patterns\"\"\"\n", "    df['body'] = df['close'] - df['open']\n", "    df['total_range'] = df['high'] - df['low']\n", "    return df\n", "\n", "def create_targets(df, pip_value=0.0001):\n", "    \"\"\"Create essential target variables\"\"\"\n", "    timeframes = {\n", "        'scalp': {'minutes': 5, 'min_pips': 5, 'max_pips': 20},\n", "        'day': {'minutes': 1440, 'min_pips': 20, 'max_pips': 100},\n", "        'swing': {'minutes': 10080, 'min_pips': 100, 'max_pips': 300},\n", "        'position': {'minutes': 10080 * 2, 'min_pips': 300, 'max_pips': 1000}\n", "    }\n", "    \n", "    for style, params in timeframes.items():\n", "        print(f\"Creating {style} targets...\")\n", "        future_prices = df['close'].shift(-params['minutes'])\n", "        price_diff = (future_prices - df['close']) / pip_value\n", "        \n", "        df[f'{style}_direction'] = np.where(price_diff > 0, 1, 0)\n", "        df[f'{style}_pips'] = abs(price_diff)\n", "        df[f'{style}_valid'] = (abs(price_diff) >= params['min_pips']) & (abs(price_diff) <= params['max_pips'])\n", "        \n", "        rolling_high = df['high'].rolling(window=params['minutes'], min_periods=1).max().shift(-params['minutes'])\n", "        rolling_low = df['low'].rolling(window=params['minutes'], min_periods=1).min().shift(-params['minutes'])\n", "        df[f'{style}_tp'] = np.where(price_diff > 0, rolling_high, rolling_low)\n", "        df[f'{style}_sl'] = np.where(price_diff > 0, rolling_low, rolling_high)\n", "        \n", "        gc.collect()\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "11972e07", "metadata": {}, "outputs": [], "source": ["# Cell 3: Main Processing Loop\n", "\n", "# Ensure we have required imports\n", "import gc\n", "import psutil\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "from sklearn.preprocessing import StandardScaler\n", "from ta.trend import SMAIndicator, EMAIndicator, MACD\n", "from ta.momentum import RSIIndicator, StochRSIIndicator\n", "from ta.volatility import BollingerBands, AverageTrueRange\n", "\n", "# Get directories from environment\n", "PROJECT_DIR = Path('/content/drive/MyDrive/V75s_Trading')\n", "DATA_DIR = PROJECT_DIR / 'historical_data'\n", "SAVE_DIR = PROJECT_DIR / 'preprocessed_data'\n", "\n", "# Feature Engineering Functions\n", "def add_technical_indicators(df):\n", "    \"\"\"Add essential technical indicators with memory optimization\"\"\"\n", "    print(\"Adding technical indicators...\")\n", "    \n", "    # Core moving averages\n", "    df['sma_20'] = SMAIndicator(close=df['close'], window=20).sma_indicator()\n", "    df['sma_50'] = SMAIndicator(close=df['close'], window=50).sma_indicator()\n", "    df['ema_20'] = EMAIndicator(close=df['close'], window=20).ema_indicator()\n", "    \n", "    # MACD\n", "    macd = MACD(close=df['close'])\n", "    df['macd'] = macd.macd()\n", "    df['macd_signal'] = macd.macd_signal()\n", "    \n", "    # RSI and Stochastic\n", "    df['rsi'] = RSIIndicator(close=df['close']).rsi()\n", "    df['stoch_rsi_k'] = StochRSIIndicator(close=df['close']).stochrsi_k()\n", "    \n", "    # Bollinger and ATR\n", "    bb = BollingerBands(close=df['close'])\n", "    df['bb_high'] = bb.bollinger_hband()\n", "    df['bb_low'] = bb.bollinger_lband()\n", "    df['atr'] = AverageTrueRange(high=df['high'], low=df['low'], close=df['close']).average_true_range()\n", "    \n", "    gc.collect()\n", "    return df\n", "\n", "def calculate_market_regime(df):\n", "    \"\"\"Simplified market regime analysis\"\"\"\n", "    df['volatility'] = (df['high'] - df['low']) / df['close']\n", "    df['trend_strength'] = abs(df['close'] - df['sma_20']) / df['close']\n", "    return df\n", "\n", "def calculate_candle_patterns(df):\n", "    \"\"\"Basic candlestick patterns\"\"\"\n", "    df['body'] = df['close'] - df['open']\n", "    df['total_range'] = df['high'] - df['low']\n", "    return df\n", "\n", "def create_targets(df, pip_value=0.0001):\n", "    \"\"\"Create essential target variables\"\"\"\n", "    timeframes = {\n", "        'scalp': {'minutes': 5, 'min_pips': 5, 'max_pips': 20},\n", "        'day': {'minutes': 1440, 'min_pips': 20, 'max_pips': 100},\n", "        'swing': {'minutes': 10080, 'min_pips': 100, 'max_pips': 300},\n", "        'position': {'minutes': 10080 * 2, 'min_pips': 300, 'max_pips': 1000}\n", "    }\n", "    \n", "    for style, params in timeframes.items():\n", "        print(f\"Creating {style} targets...\")\n", "        future_prices = df['close'].shift(-params['minutes'])\n", "        price_diff = (future_prices - df['close']) / pip_value\n", "        \n", "        df[f'{style}_direction'] = np.where(price_diff > 0, 1, 0)\n", "        df[f'{style}_pips'] = abs(price_diff)\n", "        df[f'{style}_valid'] = (abs(price_diff) >= params['min_pips']) & (abs(price_diff) <= params['max_pips'])\n", "        \n", "        rolling_high = df['high'].rolling(window=params['minutes'], min_periods=1).max().shift(-params['minutes'])\n", "        rolling_low = df['low'].rolling(window=params['minutes'], min_periods=1).min().shift(-params['minutes'])\n", "        df[f'{style}_tp'] = np.where(price_diff > 0, rolling_high, rolling_low)\n", "        df[f'{style}_sl'] = np.where(price_diff > 0, rolling_low, rolling_high)\n", "        \n", "        gc.collect()\n", "    \n", "    return df\n", "\n", "def get_memory_usage():\n", "    \"\"\"Get current memory usage in GB\"\"\"\n", "    process = psutil.Process()\n", "    mem_gb = process.memory_info().rss / (1024 * 1024 * 1024)\n", "    return f\"{mem_gb:.2f} GB\"\n", "\n", "def process_timeframe(timeframe, chunk_size=10000, sequence_length=60):\n", "    \"\"\"Process and save one timeframe completely with extreme memory optimization\"\"\"\n", "    try:\n", "        print(f\"\\nProcessing {timeframe} timeframe...\")\n", "        print(f\"Initial memory usage: {get_memory_usage()}\")\n", "        file_path = DATA_DIR / f'v75s_{timeframe}_historical.csv'\n", "        \n", "        # First pass: count total rows and validate data\n", "        print(\"First pass: counting rows and validating data...\")\n", "        total_rows = 0\n", "        for chunk in pd.read_csv(file_path, chunksize=chunk_size):\n", "            total_rows += len(chunk)\n", "        \n", "        # Calculate total sequences possible\n", "        total_sequences = total_rows - sequence_length\n", "        print(f\"Total sequences possible: {total_sequences}\")\n", "        \n", "        # Initialize memory-mapped arrays immediately\n", "        X_file = SAVE_DIR / f'X_{timeframe}.npy'\n", "        y_file = SAVE_DIR / f'y_{timeframe}.npy'\n", "        \n", "        features = ['open', 'high', 'low', 'close', 'sma_20', 'sma_50', 'ema_20',\n", "                   'macd', 'macd_signal', 'rsi', 'stoch_rsi_k', 'bb_high', 'bb_low',\n", "                   'atr', 'volatility', 'trend_strength', 'body', 'total_range']\n", "        \n", "        X_shape = (total_sequences, sequence_length, len(features))\n", "        print(f\"Creating sequence array of shape {X_shape}\")\n", "        print(f\"Memory usage before creating memmap: {get_memory_usage()}\")\n", "        \n", "        # Create memory-mapped array for X\n", "        X = np.memmap(X_file, dtype='float32', mode='w+', shape=X_shape)\n", "        y_data = []\n", "        \n", "        # Process in streaming fashion\n", "        window_buffer = []\n", "        sequence_idx = 0\n", "        chunk_start_idx = 0\n", "        \n", "        print(\"\\nSecond pass: Processing data in streaming fashion...\")\n", "        \n", "        # Initialize scaler with first chunk to get feature statistics\n", "        print(\"Initializing scaler with first chunk...\")\n", "        first_chunk = pd.read_csv(file_path, nrows=chunk_size)\n", "        first_chunk['timestamp'] = pd.to_datetime(first_chunk['epoch'], unit='s')\n", "        first_chunk.set_index('timestamp', inplace=True)\n", "        first_chunk = add_technical_indicators(first_chunk)\n", "        first_chunk = calculate_market_regime(first_chunk)\n", "        first_chunk = calculate_candle_patterns(first_chunk)\n", "        \n", "        scaler = StandardScaler()\n", "        scaler.fit(first_chunk[features])\n", "        del first_chunk\n", "        gc.collect()\n", "        \n", "        reader = pd.read_csv(file_path, chunksize=chunk_size)\n", "        for chunk_num, chunk in enumerate(reader, 1):\n", "            print(f\"\\nProcessing chunk {chunk_num}...\")\n", "            print(f\"Memory usage: {get_memory_usage()}\")\n", "            \n", "            try:\n", "                # Basic preprocessing\n", "                chunk['timestamp'] = pd.to_datetime(chunk['epoch'], unit='s')\n", "                chunk.set_index('timestamp', inplace=True)\n", "                \n", "                # Add features\n", "                chunk = add_technical_indicators(chunk)\n", "                chunk = calculate_market_regime(chunk)\n", "                chunk = calculate_candle_patterns(chunk)\n", "                chunk = create_targets(chunk)\n", "                \n", "                # Scale features\n", "                chunk_scaled = pd.DataFrame(\n", "                    scaler.transform(chunk[features]),\n", "                    columns=features,\n", "                    index=chunk.index\n", "                )\n", "                \n", "                # Update window buffer\n", "                window_buffer.extend(chunk_scaled.values)\n", "                \n", "                # Once we have enough data, start creating sequences\n", "                while len(window_buffer) >= sequence_length:\n", "                    # Create sequence\n", "                    if sequence_idx < total_sequences:\n", "                        X[sequence_idx] = window_buffer[:sequence_length]\n", "                        \n", "                        # Get target data from original chunk\n", "                        target_idx = chunk_start_idx + sequence_length\n", "                        if target_idx < len(chunk):\n", "                            y_data.append({\n", "                                'direction': chunk.iloc[target_idx][[f'{style}_direction' for style in ['scalp', 'day', 'swing', 'position']]].values,\n", "                                'pips': chunk.iloc[target_idx][[f'{style}_pips' for style in ['scalp', 'day', 'swing', 'position']]].values,\n", "                                'tp': chunk.iloc[target_idx][[f'{style}_tp' for style in ['scalp', 'day', 'swing', 'position']]].values,\n", "                                'sl': chunk.iloc[target_idx][[f'{style}_sl' for style in ['scalp', 'day', 'swing', 'position']]].values,\n", "                                'valid': chunk.iloc[target_idx][[f'{style}_valid' for style in ['scalp', 'day', 'swing', 'position']]].values\n", "                            })\n", "                        \n", "                        sequence_idx += 1\n", "                    \n", "                    # Remove oldest data point\n", "                    window_buffer.pop(0)\n", "                    chunk_start_idx += 1\n", "                \n", "                # Periodic cleanup\n", "                if chunk_num % 5 == 0:\n", "                    print(\"Performing memory cleanup...\")\n", "                    gc.collect()\n", "                    \n", "            except Exception as e:\n", "                print(f\"Error processing chunk {chunk_num}: {str(e)}\")\n", "                continue\n", "        \n", "        # Save targets\n", "        print(f\"\\nSaving {timeframe} data...\")\n", "        print(f\"Memory usage before saving: {get_memory_usage()}\")\n", "        np.save(y_file, y_data)\n", "        \n", "        # Cleanup\n", "        X.flush()\n", "        del X\n", "        del window_buffer\n", "        del y_data\n", "        gc.collect()\n", "        \n", "        print(f\"Completed processing {timeframe}\")\n", "        print(f\"Final memory usage: {get_memory_usage()}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {timeframe}: {str(e)}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "# Define and sort timeframes from largest to smallest\n", "timeframes = ['1m', '5m', '30m', '1h', '8h']\n", "timeframes.sort(reverse=True)  # Now processes: 8h, 1h, 30m, 5m, 1m\n", "\n", "print(\"\\nStarting processing with initial memory usage:\", get_memory_usage())\n", "\n", "# Process each timeframe\n", "for tf in timeframes:\n", "    process_timeframe(tf)\n", "    print(\"\\nForcing complete garbage collection...\")\n", "    for _ in range(3):  # Multiple GC passes\n", "        gc.collect()\n", "    print(f\"Memory usage after {tf}: {get_memory_usage()}\")\n", "\n", "print(\"\\nAll preprocessing complete. Data saved to:\", SAVE_DIR)"]}, {"cell_type": "code", "execution_count": null, "id": "9883e158", "metadata": {}, "outputs": [], "source": ["# Cell 4: Advanced Data Preparation and Filtering\n", "\n", "# Install ta library (Python Technical Analysis library)\n", "!pip install ta\n", "\n", "# First, mount Google Drive\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.preprocessing import RobustScaler\n", "from scipy import stats\n", "import ta\n", "from tqdm import tqdm\n", "from pathlib import Path\n", "\n", "# Define directory paths\n", "BASE_DIR = Path('/content/drive/MyDrive/V75s_Trading')\n", "DATA_DIR = BASE_DIR / 'historical_data'\n", "SAVE_DIR = BASE_DIR / 'preprocessed_data'\n", "\n", "# Create directories if they don't exist\n", "os.makedirs(SAVE_DIR, exist_ok=True)\n", "\n", "def calculate_technical_indicators(df):\n", "    \"\"\"Calculate technical indicators without volume dependency\"\"\"\n", "    # Price data\n", "    high = df['High']\n", "    low = df['Low']\n", "    close = df['Close']\n", "    open_price = df['Open']\n", "    \n", "    # Basic price indicators\n", "    df['Returns'] = close.pct_change()\n", "    df['Range'] = high - low\n", "    df['HL2'] = (high + low) / 2\n", "    df['HLC3'] = (high + low + close) / 3\n", "    df['OHLC4'] = (open_price + high + low + close) / 4\n", "    \n", "    # Trend Indicators\n", "    df['SMA_10'] = ta.trend.sma_indicator(close, window=10)\n", "    df['SMA_20'] = ta.trend.sma_indicator(close, window=20)\n", "    df['SMA_50'] = ta.trend.sma_indicator(close, window=50)\n", "    df['EMA_10'] = ta.trend.ema_indicator(close, window=10)\n", "    df['EMA_20'] = ta.trend.ema_indicator(close, window=20)\n", "    df['EMA_50'] = ta.trend.ema_indicator(close, window=50)\n", "    df['MACD'] = ta.trend.macd_diff(close)\n", "    df['ADX'] = ta.trend.adx(high, low, close)\n", "    \n", "    # Momentum Indicators\n", "    df['RSI'] = ta.momentum.rsi(close)\n", "    df['STOCH_K'] = ta.momentum.stoch(high, low, close)\n", "    df['STOCH_D'] = ta.momentum.stoch_signal(high, low, close)\n", "    df['ROC'] = ta.momentum.roc(close)\n", "    df['PPO'] = ta.momentum.ppo_signal(close)\n", "    \n", "    # Volatility Indicators\n", "    df['BB_UPPER'] = ta.volatility.bollinger_hband(close)\n", "    df['BB_MIDDLE'] = ta.volatility.bollinger_mavg(close)\n", "    df['BB_LOWER'] = ta.volatility.bollinger_lband(close)\n", "    df['ATR'] = ta.volatility.average_true_range(high, low, close)\n", "    df['KC_UPPER'] = ta.volatility.keltner_channel_hband(high, low, close)\n", "    df['KC_LOWER'] = ta.volatility.keltner_channel_lband(high, low, close)\n", "    \n", "    # Remove any NaN values using newer pandas syntax\n", "    return df.ffill().bfill()\n", "\n", "def process_csv_file(file_path):\n", "    \"\"\"Process a single CSV file and return preprocessed data\"\"\"\n", "    print(f\"\\nProcessing {file_path.name}...\")\n", "    \n", "    # Read CSV file\n", "    df = pd.read_csv(file_path)\n", "    print(f\"Original columns: {df.columns.tolist()}\")\n", "    \n", "    # Map column names\n", "    column_mapping = {\n", "        'open': 'Open',\n", "        'high': 'High',\n", "        'low': 'Low',\n", "        'close': 'Close',\n", "    }\n", "    \n", "    # Rename columns to match expected format\n", "    df = df.rename(columns={k: v for k, v in column_mapping.items() if k in df.columns})\n", "    print(f\"Mapped columns: {df.columns.tolist()}\")\n", "    \n", "    # Ensure required columns exist\n", "    required_columns = ['Open', 'High', 'Low', 'Close']\n", "    if not all(col in df.columns for col in required_columns):\n", "        missing_cols = [col for col in required_columns if col not in df.columns]\n", "        print(f\"Missing required columns: {missing_cols}\")\n", "        return None, None\n", "    \n", "    # Calculate technical indicators\n", "    df_processed = calculate_technical_indicators(df)\n", "    \n", "    # Select features for X (all numeric columns except datetime and epoch)\n", "    feature_cols = df_processed.select_dtypes(include=[np.number]).columns\n", "    X = df_processed[feature_cols].values\n", "    \n", "    # Create targets (multi-target approach)\n", "    future_returns = df_processed['Close'].pct_change().shift(-1)\n", "    y = np.column_stack([\n", "        (future_returns > 0).astype(int),  # Direction\n", "        (future_returns > 0.001).astype(int),  # Significant up move\n", "        (future_returns < -0.001).astype(int)  # Significant down move\n", "    ])[:-1]  # Remove last row since we don't have future data for it\n", "    \n", "    return X[:-1], y  # Remove last row to match target shape\n", "\n", "# Process each timeframe\n", "timeframes = {\n", "    '1m': 'v75s_1m_historical.csv',\n", "    '5m': 'v75s_5m_historical.csv',\n", "    '15m': 'v75s_15m_historical.csv',\n", "    '1h': 'v75s_1h_historical.csv',\n", "    '4h': 'v75s_4h_historical.csv',\n", "    '24h': 'v75s_24h_historical.csv'\n", "}\n", "\n", "print(\"\\nStarting data preprocessing from raw CSV files...\")\n", "\n", "for tf, filename in timeframes.items():\n", "    csv_path = DATA_DIR / filename\n", "    if not csv_path.exists():\n", "        print(f\"\\nSkipping {tf} - CSV file not found\")\n", "        continue\n", "        \n", "    try:\n", "        # Process CSV file\n", "        X, y = process_csv_file(csv_path)\n", "        \n", "        if X is None or y is None:\n", "            print(f\"Skipping {tf} - Processing failed\")\n", "            continue\n", "            \n", "        # Save preprocessed data\n", "        x_path = SAVE_DIR / f'X_{tf}.npy'\n", "        y_path = SAVE_DIR / f'y_{tf}.npy'\n", "        \n", "        np.save(str(x_path), X)\n", "        np.save(str(y_path), y)\n", "        \n", "        print(f\"Saved preprocessed data for {tf}\")\n", "        print(f\"X shape: {X.shape}, y shape: {y.shape}\")\n", "        print(f\"Number of features: {X.shape[1]}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {tf}: {str(e)}\")\n", "        continue\n", "\n", "print(\"\\nPreprocessing complete!\")\n", "\n", "# Verify the newly saved files\n", "print(\"\\nVerifying saved files:\")\n", "for tf in timeframes:\n", "    x_path = SAVE_DIR / f'X_{tf}.npy'\n", "    y_path = SAVE_DIR / f'y_{tf}.npy'\n", "    if x_path.exists() and y_path.exists():\n", "        try:\n", "            X = np.load(str(x_path), allow_pickle=False)\n", "            y = np.load(str(y_path), allow_pickle=False)\n", "            print(f\"\\n{tf} timeframe:\")\n", "            print(f\"X shape: {X.shape}\")\n", "            print(f\"y shape: {y.shape}\")\n", "        except Exception as e:\n", "            print(f\"Error loading {tf} data: {str(e)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b90b9729", "metadata": {}, "outputs": [], "source": ["# Cell 5: Optimized GPU Training for Hybrid Model\n", "\n", "# First, verify Colab runtime and GPU\n", "try:\n", "    from google.colab import drive\n", "    import torch\n", "\n", "    # Check if GPU is available\n", "    if torch.cuda.is_available():\n", "        device_name = torch.cuda.get_device_name()\n", "        print(f\"GPU is available: {device_name}\")\n", "        # Set default tensor type to cuda\n", "        torch.set_default_tensor_type('torch.cuda.FloatTensor')\n", "    else:\n", "        print(\"No GPU found. Running on CPU\")\n", "    \n", "    # Mount Google Drive\n", "    drive.mount('/content/drive')\n", "    print(\"Google Drive mounted successfully\")\n", "    \n", "except ImportError as e:\n", "    print(\"Error: This notebook should be run in Google Colab\")\n", "    raise e\n", "\n", "print(\"Starting execution with verified runtime...\")\n", "\n", "# [Rest of the imports and code remains the same]\n", "import torch\n", "from torch.utils.data import Dataset, DataLoader, random_split\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "from pathlib import Path\n", "import math\n", "from torch.nn.utils.rnn import pad_sequence\n", "import logging\n", "from typing import Dict, List, Tuple, Optional, Union\n", "from dataclasses import dataclass\n", "import numpy.typing as npt\n", "from sklearn.preprocessing import StandardScaler, RobustScaler\n", "from tqdm.notebook import tqdm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Imports completed\")\n", "\n", "# Optimized training constants\n", "SEQUENCE_LENGTH = 60\n", "FEATURE_DIM = 29\n", "EMBEDDING_DIM = 256\n", "BATCH_SIZE = 64\n", "NUM_EPOCHS = 100\n", "LEARNING_RATE = 0.001\n", "ACCURACY_THRESHOLD = 0.15\n", "VALIDATION_SPLIT = 0.2\n", "GRADIENT_CLIP = 1.0\n", "MODEL_SAVE_DIR = Path('/content/drive/MyDrive/V75s_Trading/models')\n", "\n", "# Create model save directory\n", "MODEL_SAVE_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(levelname)s - %(message)s',\n", "    force=True\n", ")\n", "logger = logging.getLogger(__name__)\n", "logger.setLevel(logging.INFO)\n", "\n", "if not logger.handlers:\n", "    console_handler = logging.StreamHandler()\n", "    console_handler.setLevel(logging.INFO)\n", "    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')\n", "    console_handler.setFormatter(formatter)\n", "    logger.addHandler(console_handler)\n", "\n", "print(\"Environment setup completed successfully\")\n", "\n", "# Optimized training constants\n", "SEQUENCE_LENGTH = 60\n", "FEATURE_DIM = 29\n", "EMBEDDING_DIM = 256\n", "BATCH_SIZE = 64  # Increased batch size for better stability\n", "NUM_EPOCHS = 100  # Adjusted number of epochs\n", "LEARNING_RATE = 0.001  # Increased initial learning rate\n", "ACCURACY_THRESHOLD = 0.15  # Reduced threshold for more realistic accuracy calculation\n", "VALIDATION_SPLIT = 0.2\n", "GRADIENT_CLIP = 1.0  # Increased gradient clip threshold\n", "\n", "@dataclass\n", "class BatchData:\n", "    \"\"\"Data class to hold batch information\"\"\"\n", "    features: torch.Tensor\n", "    targets: torch.Tensor\n", "\n", "class TimeSeriesDataset(Dataset):\n", "    \"\"\"Dataset class for V75s time series data with fixed-length sequences\"\"\"\n", "    \n", "    def __init__(self, X: Union[np.n<PERSON><PERSON>, torch.Tensor], \n", "                 y: Union[np.n<PERSON><PERSON>, <PERSON>.Tensor], \n", "                 seq_length: int = SEQUENCE_LENGTH,\n", "                 scaler: Optional[RobustScaler] = None):\n", "        \"\"\"\n", "        Initialize dataset with robust scaling and improved preprocessing\n", "        \n", "        Args:\n", "            X: Feature data of shape [N, feature_dim] or [N, seq_len, feature_dim]\n", "            y: Target data of shape [N, num_targets]\n", "            seq_length: Desired length for all sequences\n", "        \"\"\"\n", "        # Convert to numpy for preprocessing\n", "        X = X.numpy() if isinstance(X, torch.Tensor) else X\n", "        y = y.numpy() if isinstance(y, torch.Tensor) else y\n", "        \n", "        # Reshape if needed\n", "        if X.ndim == 2:\n", "            X = X.reshape(-1, X.shape[-1])\n", "        \n", "        # Initialize scaler with improved parameters\n", "        if scaler is None:\n", "            self.scaler = RobustScaler(\n", "                with_centering=True,\n", "                with_scaling=True,\n", "                quantile_range=(1.0, 99.0)  # More robust to outliers\n", "            )\n", "        else:\n", "            self.scaler = scaler\n", "            \n", "        # Scale features with extra safety checks\n", "        X_clean = np.nan_to_num(X, nan=0.0, posinf=1e6, neginf=-1e6)\n", "        X_scaled = self.scaler.fit_transform(X_clean)\n", "        \n", "        # Clip extreme values\n", "        X_scaled = np.clip(X_scaled, -10, 10)\n", "        \n", "        # Scale targets separately for each column\n", "        self.target_scalers = []\n", "        y_scaled = np.zeros_like(y, dtype=np.float32)\n", "        \n", "        for i in range(y.shape[1]):\n", "            scaler = RobustScaler(\n", "                with_centering=True,\n", "                with_scaling=True,\n", "                quantile_range=(1.0, 99.0)\n", "            )\n", "            y_scaled[:, i] = scaler.fit_transform(y[:, i].reshape(-1, 1)).ravel()\n", "            self.target_scalers.append(scaler)\n", "        \n", "        # Convert to tensors\n", "        self.X = torch.FloatTensor(X_scaled)\n", "        self.y = torch.FloatTensor(y_scaled)\n", "        \n", "        # Create sequences\n", "        self.X = self.create_sequences(self.X, seq_length)\n", "        self.seq_length = seq_length\n", "        \n", "        logger.info(f\"Dataset initialized - X shape: {self.X.shape}, y shape: {self.y.shape}\")\n", "        \n", "    def create_sequences(self, data: torch.Tensor, seq_length: int) -> torch.Tensor:\n", "        \"\"\"Create sequences for temporal modeling\"\"\"\n", "        sequences = []\n", "        for i in range(len(data) - seq_length + 1):\n", "            sequence = data[i:i + seq_length]\n", "            sequences.append(sequence)\n", "        return torch.stack(sequences)\n", "    \n", "    def __len__(self) -> int:\n", "        return len(self.X)\n", "    \n", "    def __getitem__(self, idx: int) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:\n", "        return self.X[idx], self.y[idx]\n", "    \n", "    def denormalize_targets(self, y: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"Denormalize predictions back to original scale\"\"\"\n", "        y_np = y.cpu().numpy()\n", "        y_denorm = np.zeros_like(y_np)\n", "        \n", "        for i in range(y_np.shape[1]):\n", "            y_denorm[:, i] = self.target_scalers[i].inverse_transform(\n", "                y_np[:, i].reshape(-1, 1)\n", "            ).ravel()\n", "            \n", "        return torch.FloatTensor(y_denorm).to(y.device)\n", "\n", "def collate_fn(batch: List[Tuple[torch.Tensor, torch.Tensor]]) -> BatchData:\n", "    \"\"\"\n", "    Custom collate function to handle batches of sequences.\n", "    \n", "    Args:\n", "        batch: List of tuples (features, targets)\n", "        \n", "    Returns:\n", "        BatchData containing batched features and targets\n", "    \"\"\"\n", "    try:\n", "        # Separate features and labels\n", "        sequences, labels = zip(*batch)\n", "        \n", "        # Stack into batches\n", "        features = torch.stack(sequences)  # Shape: [batch_size, sequence_length, feature_dim]\n", "        labels = torch.stack(labels)       # Shape: [batch_size, num_targets]\n", "        \n", "        return BatchData(features=features, targets=labels)\n", "    except Exception as e:\n", "        logger.error(f\"Error in collate_fn: {e}\")\n", "        logger.error(f\"Batch contents: {batch}\")\n", "        raise\n", "\n", "class V75sHybridModel(nn.Module):\n", "    \"\"\"Hybrid model architecture\"\"\"\n", "    def __init__(self, input_dim: int, hidden_dim: int = EMBEDDING_DIM, \n", "                 num_layers: int = 2, num_heads: int = 4, \n", "                 dropout: float = 0.2, num_targets: int = 3):\n", "        super().__init__()\n", "        \n", "        self.input_dim = input_dim\n", "        self.hidden_dim = hidden_dim\n", "        \n", "        # Input processing with CNN embedding\n", "        self.input_projection = nn.Sequential(\n", "            nn.Linear(input_dim, hidden_dim),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>(hidden_dim),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout)\n", "        )\n", "        \n", "        # Multi-scale CNN blocks\n", "        self.cnn_blocks = nn.ModuleList([\n", "            nn.Sequential(\n", "                nn.Conv1d(hidden_dim, hidden_dim, kernel_size=k, padding=k//2),\n", "                nn.BatchNorm1d(hidden_dim),\n", "                nn.ReLU(),\n", "                nn.Conv1d(hidden_dim, hidden_dim, kernel_size=1),\n", "                nn.BatchNorm1d(hidden_dim),\n", "                nn.ReLU(),\n", "                nn.Dropout(dropout)\n", "            ) for k in [3, 5, 7]\n", "        ])\n", "        \n", "        # BiLSTM\n", "        self.lstm = nn.LSTM(\n", "            input_size=hidden_dim,\n", "            hidden_size=hidden_dim,\n", "            num_layers=num_layers,\n", "            batch_first=True,\n", "            bidirectional=True,\n", "            dropout=dropout if num_layers > 1 else 0\n", "        )\n", "        \n", "        self.lstm_proj = nn.Linear(hidden_dim * 2, hidden_dim)\n", "        \n", "        self.attention = nn.Multihead<PERSON><PERSON>tion(\n", "            embed_dim=hidden_dim,\n", "            num_heads=num_heads,\n", "            dropout=dropout,\n", "            batch_first=True\n", "        )\n", "        \n", "        fusion_size = hidden_dim * 4\n", "        \n", "        self.fusion = nn.Sequential(\n", "            nn.Linear(fusion_size, hidden_dim * 2),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>(hidden_dim * 2),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(hidden_dim * 2, hidden_dim),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(hidden_dim, num_targets)\n", "        )\n", "        \n", "        self.apply(self._init_weights)\n", "        \n", "    def _init_weights(self, module):\n", "        if isinstance(module, (nn.<PERSON>, nn.Conv1d)):\n", "            nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')\n", "            if module.bias is not None:\n", "                nn.init.constant_(module.bias, 0)\n", "        elif isinstance(module, (nn.BatchNorm1d, nn.LayerNorm)):\n", "            nn.init.constant_(module.weight, 1)\n", "            nn.init.constant_(module.bias, 0)\n", "            \n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        batch_size, seq_len = x.size(0), x.size(1)\n", "        \n", "        x = self.input_projection(x)\n", "        \n", "        x_cnn = x.transpose(1, 2)\n", "        cnn_outputs = []\n", "        for cnn in self.cnn_blocks:\n", "            cnn_out = cnn(x_cnn)\n", "            cnn_outputs.append(cnn_out)\n", "        \n", "        x_cnn = torch.stack(cnn_outputs).mean(0)\n", "        x_cnn = x_cnn.transpose(1, 2)\n", "        \n", "        lstm_out, (h_n, _) = self.lstm(x_cnn)\n", "        lstm_out = self.lstm_proj(lstm_out)\n", "        \n", "        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)\n", "        attn_out = attn_out + lstm_out\n", "        \n", "        last_lstm = lstm_out[:, -1]\n", "        last_attn = attn_out[:, -1]\n", "        max_pool = torch.max(x_cnn, dim=1)[0]\n", "        avg_pool = torch.mean(x_cnn, dim=1)\n", "        \n", "        fusion_input = torch.cat([\n", "            last_lstm, last_attn, max_pool, avg_pool\n", "        ], dim=1)\n", "        \n", "        out = self.fusion(fusion_input)\n", "        \n", "        return out\n", "\n", "def calculate_metrics(outputs: torch.Tensor, targets: torch.Tensor, \n", "                     dataset: TimeSeriesDataset,\n", "                     threshold: float = ACCURACY_THRESHOLD) -> Tuple[float, Dict[str, float]]:\n", "    with torch.no_grad():\n", "        loss = F.mse_loss(outputs, targets)\n", "        \n", "        correct = torch.abs(outputs - targets) <= threshold\n", "        accuracies = correct.float().mean(dim=0)\n", "        \n", "        denorm_outputs = dataset.denormalize_targets(outputs)\n", "        denorm_targets = dataset.denormalize_targets(targets)\n", "        \n", "        denorm_correct = torch.abs(denorm_outputs - denorm_targets) <= threshold\n", "        denorm_accuracies = denorm_correct.float().mean(dim=0)\n", "        \n", "        metrics = {\n", "            'loss': loss.item(),\n", "            'avg_accuracy': correct.float().mean().item() * 100,\n", "            'target1_acc': accuracies[0].item() * 100,\n", "            'target2_acc': accuracies[1].item() * 100,\n", "            'target3_acc': accuracies[2].item() * 100,\n", "            'denorm_avg_acc': denorm_correct.float().mean().item() * 100,\n", "        }\n", "        \n", "        return loss, metrics\n", "\n", "def train_model():\n", "    \"\"\"Enhanced training function with improved stability and checkpointing\"\"\"\n", "    try:\n", "        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "        logger.info(f\"Using device: {device}\")\n", "        \n", "        model = V75sHybridModel(\n", "            input_dim=FEATURE_DIM,\n", "            hidden_dim=EMBEDDING_DIM,\n", "            num_layers=2,\n", "            num_heads=4,\n", "            dropout=0.2,\n", "            num_targets=3\n", "        ).to(device)\n", "        \n", "        logger.info(\"Model initialized successfully\")\n", "        \n", "        base_path = Path('/content/drive/MyDrive/V75s_Trading/preprocessed_data')\n", "        logger.info(f\"Loading data from: {base_path}\")\n", "        \n", "        if not base_path.exists():\n", "            raise ValueError(f\"Data directory not found at {base_path}\")\n", "        \n", "        x_path = base_path / 'X_1m.npy'\n", "        y_path = base_path / 'y_1m.npy'\n", "        \n", "        if not x_path.exists() or not y_path.exists():\n", "            raise FileNotFoundError(f\"Data files not found at {x_path} or {y_path}\")\n", "        \n", "        X = np.load(x_path)\n", "        y = np.load(y_path)\n", "        \n", "        logger.info(f\"Data loaded - X shape: {X.shape}, y shape: {y.shape}\")\n", "        \n", "        dataset = TimeSeriesDataset(X, y)\n", "        train_size = int((1 - VALIDATION_SPLIT) * len(dataset))\n", "        val_size = len(dataset) - train_size\n", "        \n", "        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])\n", "        \n", "        train_loader = DataLoader(\n", "            train_dataset,\n", "            batch_size=BATCH_SIZE,\n", "            shuffle=True,\n", "            num_workers=2,  # Increased workers\n", "            pin_memory=True,\n", "            collate_fn=collate_fn\n", "        )\n", "        \n", "        val_loader = DataLoader(\n", "            val_dataset,\n", "            batch_size=BATCH_SIZE,\n", "            shuffle=False,\n", "            num_workers=2,\n", "            pin_memory=True,\n", "            collate_fn=collate_fn\n", "        )\n", "        \n", "        # Improved optimizer setup with weight decay\n", "        optimizer = torch.optim.AdamW(\n", "            model.parameters(),\n", "            lr=LEARNING_RATE,\n", "            weight_decay=0.01,\n", "            betas=(0.9, 0.999),\n", "            eps=1e-8\n", "        )\n", "        \n", "        # Better learning rate scheduling\n", "        scheduler = torch.optim.lr_scheduler.OneCycleLR(\n", "            optimizer,\n", "            max_lr=LEARNING_RATE,\n", "            epochs=NUM_EPOCHS,\n", "            steps_per_epoch=len(train_loader),\n", "            pct_start=0.2,  # Faster warmup\n", "            anneal_strategy='cos',\n", "            final_div_factor=1e3\n", "        )\n", "        \n", "        criterion = nn.MS<PERSON><PERSON>()\n", "        \n", "        # Training state tracking\n", "        best_val_loss = float('inf')\n", "        best_accuracy = 0.0\n", "        patience = 20\n", "        patience_counter = 0\n", "        min_epochs = 30\n", "        \n", "        # Save the initial model state\n", "        save_checkpoint(model, optimizer, scheduler, 0, float('inf'), \n", "                       {'avg_accuracy': 0}, MODEL_SAVE_DIR / 'initial_model.pt')\n", "        \n", "        logger.info(f\"\\nStarting training for {NUM_EPOCHS} epochs\")\n", "        \n", "        for epoch in range(NUM_EPOCHS):\n", "            model.train()\n", "            epoch_loss = 0\n", "            epoch_metrics = {\n", "                'avg_accuracy': 0,\n", "                'target1_acc': 0,\n", "                'target2_acc': 0,\n", "                'target3_acc': 0\n", "            }\n", "            \n", "            progress_bar = tqdm(train_loader, desc=f\"Epoch {epoch+1}/{NUM_EPOCHS}\")\n", "            num_batches = 0\n", "            \n", "            for batch in progress_bar:\n", "                features = batch.features.to(device)\n", "                targets = batch.targets.to(device)\n", "                \n", "                optimizer.zero_grad()\n", "                outputs = model(features)\n", "                loss, batch_metrics = calculate_metrics(outputs, targets, dataset)\n", "                \n", "                loss.backward()\n", "                torch.nn.utils.clip_grad_norm_(model.parameters(), GRADIENT_CLIP)\n", "                optimizer.step()\n", "                scheduler.step()\n", "                \n", "                epoch_loss += loss.item()\n", "                for key in epoch_metrics:\n", "                    epoch_metrics[key] += batch_metrics[key]\n", "                num_batches += 1\n", "                \n", "                # Update progress bar with moving averages\n", "                progress_bar.set_postfix({\n", "                    'loss': f\"{loss.item():.4f}\",\n", "                    'acc': f\"{batch_metrics['avg_accuracy']:.2f}%\"\n", "                })\n", "            \n", "            # Calculate epoch averages\n", "            epoch_loss /= num_batches\n", "            for key in epoch_metrics:\n", "                epoch_metrics[key] /= num_batches\n", "            \n", "            # Validation phase\n", "            model.eval()\n", "            val_loss = 0\n", "            val_metrics = {\n", "                'avg_accuracy': 0,\n", "                'target1_acc': 0,\n", "                'target2_acc': 0,\n", "                'target3_acc': 0\n", "            }\n", "            num_val_batches = 0\n", "            \n", "            with torch.no_grad():\n", "                for batch in val_loader:\n", "                    features = batch.features.to(device)\n", "                    targets = batch.targets.to(device)\n", "                    \n", "                    outputs = model(features)\n", "                    loss, batch_metrics = calculate_metrics(outputs, targets, dataset)\n", "                    \n", "                    val_loss += loss.item()\n", "                    for key in val_metrics:\n", "                        val_metrics[key] += batch_metrics[key]\n", "                    num_val_batches += 1\n", "            \n", "            val_loss /= num_val_batches\n", "            for key in val_metrics:\n", "                val_metrics[key] /= num_val_batches\n", "            \n", "            # Detailed epoch summary\n", "            logger.info(\n", "                f\"\\nEpoch {epoch+1}/{NUM_EPOCHS}:\"\n", "                f\"\\nTrain - Loss: {epoch_loss:.4f}, Accuracy: {epoch_metrics['avg_accuracy']:.2f}%\"\n", "                f\"\\nTarget Accuracies - T1: {epoch_metrics['target1_acc']:.2f}%, \"\n", "                f\"T2: {epoch_metrics['target2_acc']:.2f}%, \"\n", "                f\"T3: {epoch_metrics['target3_acc']:.2f}%\"\n", "                f\"\\nVal - Loss: {val_loss:.4f}, Accuracy: {val_metrics['avg_accuracy']:.2f}%\"\n", "            )\n", "            \n", "            # Save model on multiple conditions\n", "            if val_loss < best_val_loss:\n", "                best_val_loss = val_loss\n", "                save_checkpoint(model, optimizer, scheduler, epoch, val_loss, \n", "                              val_metrics, MODEL_SAVE_DIR / 'best_loss_model.pt')\n", "            \n", "            if val_metrics['avg_accuracy'] > best_accuracy:\n", "                best_accuracy = val_metrics['avg_accuracy']\n", "                save_checkpoint(model, optimizer, scheduler, epoch, val_loss,\n", "                              val_metrics, MODEL_SAVE_DIR / 'best_accuracy_model.pt')\n", "                \n", "                # Save additional milestone models\n", "                if best_accuracy >= 90 and (int(best_accuracy) % 1 == 0):  # Save at each integer milestone >=90\n", "                    save_checkpoint(model, optimizer, scheduler, epoch, val_loss,\n", "                                  val_metrics, MODEL_SAVE_DIR / f'model_acc_{int(best_accuracy)}.pt')\n", "            \n", "            # Save periodic checkpoints every 10 epochs\n", "            if (epoch + 1) % 10 == 0:\n", "                save_checkpoint(model, optimizer, scheduler, epoch, val_loss,\n", "                              val_metrics, MODEL_SAVE_DIR / f'checkpoint_epoch_{epoch+1}.pt')\n", "        \n", "        return model, {\n", "            'final_train_loss': epoch_loss,\n", "            'final_train_acc': epoch_metrics['avg_accuracy'],\n", "            'final_val_loss': val_loss,\n", "            'final_val_acc': val_metrics['avg_accuracy']\n", "        }\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Training failed: {str(e)}\")\n", "        logger.exception(\"Detailed error trace:\")\n", "        raise\n", "\n", "def load_saved_model(model_path: Union[str, Path], device: torch.device = None) -> V75sHybridModel:\n", "    if device is None:\n", "        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    \n", "    checkpoint = torch.load(model_path, map_location=device)\n", "    model = V75sHybridModel(\n", "        input_dim=FEATURE_DIM,\n", "        hidden_dim=EMBEDDING_DIM,\n", "        num_layers=2,\n", "        num_heads=4,\n", "        dropout=0.2,\n", "        num_targets=3\n", "    ).to(device)\n", "    \n", "    model.load_state_dict(checkpoint['model_state_dict'])\n", "    return model"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}