//+------------------------------------------------------------------+
//|                                                   CGJSpike8.mq5 |
//|                        Copyright 2025, Gemini at Google |
//|                                             https://gemini.google.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Gemini at Google"
#property link      "https://gemini.google.com"
#property version   "1.0"
#property strict

#include <Trade/Trade.mqh>

//--- Input Parameters
input int      atr_period = 14;                   // Period for ATR calculation
input double   spike_multiplier = 3.0;            // Multiplier for ATR to detect a spike
input double   lot_size = 0.1;                    // Trade volume
input int      stop_loss_pips = 200;              // Stop Loss in pips
input int      take_profit_pips = 600;            // Take Profit in pips
input ulong    magic_number = 888;                // Magic number for trades

//--- Global variables
CTrade trade;
int    atr_handle;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
   //--- Initialize trade object
   trade.SetExpertMagicNumber(magic_number);
   trade.SetMarginMode();
   
   //--- Initialize ATR indicator
   atr_handle = iATR(_Symbol, PERIOD_M1, atr_period);
   if(atr_handle == INVALID_HANDLE)
     {
      Print("Error creating ATR indicator");
      return(INIT_FAILED);
     }

   Print("CGJSpike8 EA Initialized Successfully.");
   return(INIT_SUCCEEDED);
  }

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   //--- Clean up
   IndicatorRelease(atr_handle);
   Print("CGJSpike8 EA Deinitialized.");
  }

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
   //--- Only run on the formation of a new bar
   static datetime last_bar_time = 0;
   MqlRates rates[1];
   if(CopyRates(_Symbol, PERIOD_M1, 0, 1, rates) > 0)
   {
      if(rates[0].time == last_bar_time)
      {
         return;
      }
      last_bar_time = rates[0].time;
   }
   else
   {
      return; // Not enough bars yet
   }


   //--- Check if we can open a new trade
   if(PositionsTotal() > 0)
     {
      Print("PositionsTotal() > 0. Not opening new trade.");
      return;
     }

   //--- Get historical data for the last 2 bars
   MqlRates analysis_rates[2];
   if(CopyRates(_Symbol, PERIOD_M1, 1, 2, analysis_rates) < 2)
     {
      Print("Not enough bars for analysis");
      return;
     }
     
   //--- Get ATR value
   double atr_value_buffer[1];
   if(CopyBuffer(atr_handle, 0, 1, 1, atr_value_buffer) < 1)
     {
      Print("Could not get ATR value");
      return;
     }
   double atr_value = atr_value_buffer[0];

   //--- Analyze the most recently closed candle (at index 0 of our copied rates array)
   MqlRates last_candle = analysis_rates[0];
   double candle_range = last_candle.high - last_candle.low;
   double spike_threshold = atr_value * spike_multiplier;

   //--- Diagnostic Print Statements ---
   PrintFormat("New Bar Check | Time: %s | Candle Range: %.5f | ATR: %.5f | Spike Threshold: %.5f", 
               TimeToString(last_bar_time, TIME_SECONDS), candle_range, atr_value, spike_threshold);

   //--- Check for a spike
   if(candle_range > spike_threshold)
     {
      PrintFormat("Spike detected! Candle Range: %.5f > Spike Threshold: %.5f", candle_range, spike_threshold);
      double sl, tp;
      //--- Determine spike direction and trade
      if(last_candle.close > last_candle.open) // Bullish spike
        {
         //--- Open a SELL trade (fading the spike)
         sl = SymbolInfoDouble(_Symbol, SYMBOL_ASK) + stop_loss_pips * _Point;
         tp = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - take_profit_pips * _Point;
         PrintFormat("SELL Order | Price: %.5f | SL: %.5f | TP: %.5f", SymbolInfoDouble(_Symbol, SYMBOL_ASK), sl, tp);
         if(trade.Sell(lot_size, _Symbol, 0, sl, tp, "Sell after bullish spike"))
         {
            Print("SELL order successfully sent.");
         }
         else
         {
            PrintFormat("SELL order failed. Error: %d", GetLastError());
         }
        }
      else // Bearish spike
        {
         //--- Open a BUY trade (fading the spike)
         sl = SymbolInfoDouble(_Symbol, SYMBOL_BID) - stop_loss_pips * _Point;
         tp = SymbolInfoDouble(_Symbol, SYMBOL_BID) + take_profit_pips * _Point;
         PrintFormat("BUY Order | Price: %.5f | SL: %.5f | TP: %.5f", SymbolInfoDouble(_Symbol, SYMBOL_BID), sl, tp);
         if(trade.Buy(lot_size, _Symbol, 0, sl, tp, "Buy after bearish spike"))
         {
            Print("BUY order successfully sent.");
         }
         else
         {
            PrintFormat("BUY order failed. Error: %d", GetLastError());
         }
        }
     }
  }
//+------------------------------------------------------------------+
