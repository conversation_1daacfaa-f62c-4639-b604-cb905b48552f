//+------------------------------------------------------------------+
//|                                           403Spike8Detector.mq5 |
//|                                    Professional V75 Spike Trader |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "V75 Spike Trading EA"
#property version   "1.00"
#property description "Professional EA for trading Volatility 75 Index spikes"

//--- Input Parameters
input group "=== SPIKE DETECTION ==="
input double   MinSpikeSize = 15.0;          // Minimum spike size in points (more sensitive for V75 1s)
input double   MinBodyRatio = 0.15;          // Minimum body ratio (reduced for more opportunities)
input double   MinATRMultiplier = 0.8;       // Minimum ATR multiplier (reduced based on real ATR ~8-10)
input bool     UseVolumeFilter = false;      // Disable volume filter (causing too many rejections)
input double   VolumeMultiplier = 1.1;       // Volume spike multiplier (reduced)

input group "=== RISK MANAGEMENT ==="
input double   FixedLot = 0.1;               // Fixed lot size
input double   StopLoss = 500.0;             // Stop loss in PRICE POINTS (V75 1s needs large values)
input double   TakeProfit = 2000.0;          // Take profit in PRICE POINTS (for meaningful profit)
input int      MaxTradeDuration = 600;       // Max trade duration in seconds (increased for bigger moves)
input double   EmergencyProfitLevel = 3000.0; // Emergency profit close level (price points)
input double   EmergencyLossLevel = 1000.0;   // Emergency loss close level (price points)

input group "=== TRADE SETTINGS ==="
input bool     UseTrendFollowing = true;     // TRUE = Follow spike direction (RECOMMENDED)
input bool     UseSimpleStrategy = true;     // Use simple spike strategy when indicators fail
input int      CooldownSeconds = 30;         // Cooldown between trades
input int      MagicNumber = 12345;          // Magic number for identification
input string   TradeComment = "V75_SPIKE";   // Trade comment prefix

//--- Global Variables
int            atr_handle;
int            macd_handle;
int            rsi_handle;
double         atr_buffer[];
double         macd_main[];
double         macd_signal[];
double         rsi_buffer[];
datetime       last_bar_time = 0;
datetime       last_trade_time = 0;
int            total_trades = 0;
int            winning_trades = 0;
int            losing_trades = 0;
double         total_profit = 0.0;
double         total_loss = 0.0;
ulong          current_position_ticket = 0;
double         current_position_open_price = 0.0;
ENUM_POSITION_TYPE current_position_type = POSITION_TYPE_BUY;

//--- Trade structures
MqlTradeRequest request;
MqlTradeResult result;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize ATR indicator
    atr_handle = iATR(_Symbol, PERIOD_M1, 14);
    if(atr_handle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to create ATR indicator handle");
        return INIT_FAILED;
    }

    // Initialize MACD indicator
    macd_handle = iMACD(_Symbol, PERIOD_M1, 12, 26, 9, PRICE_CLOSE);
    if(macd_handle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to initialize MACD indicator");
        return INIT_FAILED;
    }

    // Initialize RSI indicator
    rsi_handle = iRSI(_Symbol, PERIOD_M1, 14, PRICE_CLOSE);
    if(rsi_handle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to initialize RSI indicator");
        return INIT_FAILED;
    }

    // Set arrays as series
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);
    ArraySetAsSeries(rsi_buffer, true);
    
    // Initialize trade structures
    ZeroMemory(request);
    ZeroMemory(result);
    
    // Validate symbol and timeframe - Updated for V75 1s
    if(StringFind(_Symbol, "Volatility 75 (1s)") == -1 && StringFind(_Symbol, "V75") == -1)
    {
        Print("WARNING: This EA is designed for Volatility 75 (1s) Index");
    }

    if(_Period != PERIOD_M1)
    {
        Print("WARNING: This EA is optimized for M1 timeframe");
    }

    Print("=== 403Spike8Detector EA Initialized ===");
    Print("Symbol: ", _Symbol);
    Print("Timeframe: ", EnumToString(_Period));
    Print("Min Spike Size: ", MinSpikeSize, " points");
    Print("Strategy: TREND FOLLOWING (Fixed for V75 1s)");
    Print("Indicators: ATR + MACD + RSI");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);
    if(macd_handle != INVALID_HANDLE)
        IndicatorRelease(macd_handle);
    if(rsi_handle != INVALID_HANDLE)
        IndicatorRelease(rsi_handle);

    // Print final statistics
    PrintStatistics();

    Print("=== 403Spike8Detector EA Deinitialized ===");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Track position closures first
    TrackPositionClosure();

    // Check for new bar
    if(!IsNewBar())
        return;

    // Check existing positions first
    CheckPositions();

    // Check cooldown period
    if(TimeCurrent() - last_trade_time < CooldownSeconds)
        return;

    // Look for new trade opportunities only when no positions exist
    if(PositionsTotal() == 0)
    {
        CheckForSpike();
    }
}

//+------------------------------------------------------------------+
//| Check for new bar formation                                     |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime current_bar_time = iTime(_Symbol, PERIOD_M1, 0);
    if(current_bar_time != last_bar_time)
    {
        last_bar_time = current_bar_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check for spike patterns                                        |
//+------------------------------------------------------------------+
void CheckForSpike()

    // Get previous bar data (completed candle)
    double high = iHigh(_Symbol, PERIOD_M1, 1);
    double low = iLow(_Symbol, PERIOD_M1, 1);
    double open = iOpen(_Symbol, PERIOD_M1, 1);
    double close = iClose(_Symbol, PERIOD_M1, 1);

    // Validate data
    if(high == 0 || low == 0 || open == 0 || close == 0)
    {
        Print("ERROR: Invalid price data");
        return;
    }

    // Get ATR data
    if(CopyBuffer(atr_handle, 0, 1, 1, atr_buffer) <= 0)
    {
        Print("ERROR: Failed to get ATR data");
        return;
    }

    // Get MACD data
    if(CopyBuffer(macd_handle, 0, 1, 2, macd_main) <= 0 ||
       CopyBuffer(macd_handle, 1, 1, 2, macd_signal) <= 0)
    {
        Print("ERROR: Failed to get MACD data");
        return;
    }

    // Get RSI data
    if(CopyBuffer(rsi_handle, 0, 1, 2, rsi_buffer) <= 0)
    {
        Print("ERROR: Failed to get RSI data");
        return;
    }

    double atr_value = atr_buffer[0];
    if(atr_value <= 0)
    {
        Print("ERROR: Invalid ATR value");
        return;
    }
    
    // Calculate spike metrics
    double spike_range = (high - low) / _Point;
    double body_size = MathAbs(close - open) / _Point;
    double body_ratio = body_size / spike_range;
    double atr_points = atr_value / _Point;
    double atr_multiplier = spike_range / atr_points;

    // Get volume data for additional confirmation
    long current_volume = iVolume(_Symbol, PERIOD_M1, 1);
    long prev_volume = iVolume(_Symbol, PERIOD_M1, 2);
    double volume_ratio = (prev_volume > 0) ? (double)current_volume / prev_volume : 1.0;

    // Get indicator values
    double macd_current = macd_main[0];
    double macd_previous = macd_main[1];
    double macd_signal_current = macd_signal[0];
    double rsi_current = rsi_buffer[0];
    double rsi_previous = rsi_buffer[1];

    // Calculate additional spike quality metrics
    double upper_wick = high - MathMax(open, close);
    double lower_wick = MathMin(open, close) - low;
    double upper_wick_ratio = upper_wick / spike_range;
    double lower_wick_ratio = lower_wick / spike_range;

    // Apply basic spike validation criteria with debug info
    if(spike_range < MinSpikeSize)
    {
        Print("REJECTED: Spike too small. Range: ", DoubleToString(spike_range, 1), " < Min: ", MinSpikeSize);
        return;
    }

    if(body_ratio < MinBodyRatio)
    {
        Print("REJECTED: Body ratio too small. Ratio: ", DoubleToString(body_ratio, 3), " < Min: ", MinBodyRatio);
        return;
    }

    if(atr_multiplier < MinATRMultiplier)
    {
        Print("REJECTED: ATR multiplier too small. Multiplier: ", DoubleToString(atr_multiplier, 2), " < Min: ", MinATRMultiplier);
        return;
    }

    // Volume confirmation (if enabled)
    if(UseVolumeFilter && volume_ratio < VolumeMultiplier)
    {
        Print("REJECTED: Volume filter failed. Current: ", current_volume, " Previous: ", prev_volume, " Ratio: ", DoubleToString(volume_ratio, 2));
        return;
    }

    Print("SPIKE PASSED BASIC FILTERS:");
    Print("  Range: ", DoubleToString(spike_range, 1), " points");
    Print("  Body Ratio: ", DoubleToString(body_ratio, 3));
    Print("  ATR Multiplier: ", DoubleToString(atr_multiplier, 2));
    Print("  Volume Ratio: ", DoubleToString(volume_ratio, 2));

    // Determine spike direction
    bool is_bullish_spike = (close > open);

    // STRATEGY LOGIC: Follow the spike momentum (proven more profitable)
    bool buy_signal = false;
    bool sell_signal = false;

    Print("SPIKE ANALYSIS:");
    Print("  Spike Direction: ", is_bullish_spike ? "BULLISH (Green candle)" : "BEARISH (Red candle)");
    Print("  Strategy: ", UseTrendFollowing ? "TREND FOLLOWING (trade WITH spike)" : "CONTRARIAN (trade AGAINST spike)");

    if(UseTrendFollowing)
    {
        // TREND FOLLOWING: Trade WITH the spike direction
        if(is_bullish_spike)
        {
            // Bullish spike - BUY (follow the momentum UP)
            buy_signal = true;
            Print("  → Looking for BUY signal (following bullish spike)");

        // MACD confirmation - MACD should be rising and above signal (relaxed)
        if(macd_current <= macd_signal_current)
        {
            Print("BUY REJECTED: MACD not above signal. MACD: ", DoubleToString(macd_current, 5), " Signal: ", DoubleToString(macd_signal_current, 5));
            buy_signal = false;
        }

        // RSI confirmation - should not be extremely overbought (relaxed from 70 to 80)
        if(rsi_current >= 80)
        {
            Print("BUY REJECTED: RSI overbought. RSI: ", DoubleToString(rsi_current, 1));
            buy_signal = false;
        }

        // Strong momentum confirmation (relaxed thresholds)
        if(body_ratio < 0.2 || atr_multiplier < 0.8)
        {
            Print("BUY REJECTED: Weak momentum. Body: ", DoubleToString(body_ratio, 3), " ATR: ", DoubleToString(atr_multiplier, 2));
            buy_signal = false;
        }
        }
        else
        {
            // Bearish spike - SELL (follow the momentum DOWN)
            sell_signal = true;
            Print("  → Looking for SELL signal (following bearish spike)");

        // MACD confirmation - MACD should be falling and below signal (relaxed)
        if(macd_current >= macd_signal_current)
        {
            Print("SELL REJECTED: MACD not below signal. MACD: ", DoubleToString(macd_current, 5), " Signal: ", DoubleToString(macd_signal_current, 5));
            sell_signal = false;
        }

        // RSI confirmation - should not be extremely oversold (relaxed from 30 to 20)
        if(rsi_current <= 20)
        {
            Print("SELL REJECTED: RSI oversold. RSI: ", DoubleToString(rsi_current, 1));
            sell_signal = false;
        }

        // Strong momentum confirmation (relaxed thresholds)
        if(body_ratio < 0.2 || atr_multiplier < 0.8)
        {
            Print("SELL REJECTED: Weak momentum. Body: ", DoubleToString(body_ratio, 3), " ATR: ", DoubleToString(atr_multiplier, 2));
            sell_signal = false;
        }
    }
    else
    {
        // CONTRARIAN: Trade AGAINST the spike direction (less reliable)
        if(is_bullish_spike)
        {
            // Bullish spike - SELL (expecting reversal DOWN)
            sell_signal = true;
            Print("  → Looking for SELL signal (contrarian - expecting reversal after bullish spike)");

            // Same MACD/RSI checks but reversed logic
            if(macd_current >= macd_signal_current)
            {
                Print("SELL REJECTED: MACD not below signal for contrarian. MACD: ", DoubleToString(macd_current, 5));
                sell_signal = false;
            }

            if(rsi_current <= 20)
            {
                Print("SELL REJECTED: RSI too low for contrarian. RSI: ", DoubleToString(rsi_current, 1));
                sell_signal = false;
            }
        }
        else
        {
            // Bearish spike - BUY (expecting reversal UP)
            buy_signal = true;
            Print("  → Looking for BUY signal (contrarian - expecting reversal after bearish spike)");

            // Same MACD/RSI checks but reversed logic
            if(macd_current <= macd_signal_current)
            {
                Print("BUY REJECTED: MACD not above signal for contrarian. MACD: ", DoubleToString(macd_current, 5));
                buy_signal = false;
            }

            if(rsi_current >= 80)
            {
                Print("BUY REJECTED: RSI too high for contrarian. RSI: ", DoubleToString(rsi_current, 1));
                buy_signal = false;
            }
        }
    }

    // Determine final trade direction
    ENUM_ORDER_TYPE order_type = ORDER_TYPE_BUY; // Initialize to prevent error
    bool execute_trade = false;

    if(buy_signal)
    {
        order_type = ORDER_TYPE_BUY;
        execute_trade = true;
    }
    else if(sell_signal)
    {
        order_type = ORDER_TYPE_SELL;
        execute_trade = true;
    }

    if(!execute_trade && UseSimpleStrategy)
    {
        // Fallback: Simple strategy respecting trend-following setting
        Print("Using SIMPLE STRATEGY (indicators failed)");
        Print("  Strategy: ", UseTrendFollowing ? "TREND FOLLOWING" : "CONTRARIAN");

        if(spike_range >= 20 && atr_multiplier >= 1.0)
        {
            if(UseTrendFollowing)
            {
                // Follow the spike
                if(is_bullish_spike)
                {
                    order_type = ORDER_TYPE_BUY;
                    execute_trade = true;
                    Print("SIMPLE BUY: Following bullish spike momentum");
                }
                else
                {
                    order_type = ORDER_TYPE_SELL;
                    execute_trade = true;
                    Print("SIMPLE SELL: Following bearish spike momentum");
                }
            }
            else
            {
                // Trade against the spike
                if(is_bullish_spike)
                {
                    order_type = ORDER_TYPE_SELL;
                    execute_trade = true;
                    Print("SIMPLE SELL: Contrarian against bullish spike");
                }
                else
                {
                    order_type = ORDER_TYPE_BUY;
                    execute_trade = true;
                    Print("SIMPLE BUY: Contrarian against bearish spike");
                }
            }
        }
    }

    if(!execute_trade)
    {
        Print("No valid trading signal found (both strategies failed)");
        return;
    }
    
    // Log spike detection with enhanced details
    Print("=== TREND-FOLLOWING SIGNAL CONFIRMED ===");
    Print("  Spike Range: ", DoubleToString(spike_range, 1), " points");
    Print("  Body Ratio: ", DoubleToString(body_ratio, 3));
    Print("  ATR Multiplier: ", DoubleToString(atr_multiplier, 2));
    Print("  Volume Ratio: ", DoubleToString(volume_ratio, 2));
    Print("  MACD: ", DoubleToString(macd_current, 5), " | Signal: ", DoubleToString(macd_signal_current, 5));
    Print("  RSI: ", DoubleToString(rsi_current, 1), " | Previous: ", DoubleToString(rsi_previous, 1));
    Print("  Spike Direction: ", is_bullish_spike ? "Bullish" : "Bearish");
    Print("  Trade Signal: ", buy_signal ? "BUY" : "SELL");
    Print("  Order Type: ", EnumToString(order_type));
    Print("========================================");
    
    // Execute trade
    ExecuteTrade(order_type);
}

//+------------------------------------------------------------------+
//| Execute trade based on spike detection                          |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE order_type)
{
    // Double-check that we don't have existing positions
    if(PositionsTotal() > 0)
    {
        Print("WARNING: Existing position found, skipping new trade");
        return;
    }

    // Get current market prices
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    if(ask <= 0 || bid <= 0)
    {
        Print("ERROR: Invalid market prices. Ask: ", ask, " Bid: ", bid);
        return;
    }

    // Prepare trade request
    ZeroMemory(request);
    ZeroMemory(result);

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = FixedLot;
    request.type = order_type;
    request.price = (order_type == ORDER_TYPE_BUY) ? ask : bid;
    request.deviation = 5;  // Increased deviation for V75
    request.magic = MagicNumber;
    request.comment = TradeComment + "_" + TimeToString(TimeCurrent(), TIME_SECONDS);
    request.type_filling = ORDER_FILLING_FOK;  // Fill or Kill

    Print("Preparing trade:");
    Print("  Type: ", EnumToString(order_type));
    Print("  Volume: ", request.volume);
    Print("  Price: ", DoubleToString(request.price, _Digits));
    Print("  Ask: ", DoubleToString(ask, _Digits));
    Print("  Bid: ", DoubleToString(bid, _Digits));

    // Validate lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    if(request.volume < min_lot || request.volume > max_lot)
    {
        Print("ERROR: Invalid lot size. Min: ", min_lot, " Max: ", max_lot, " Current: ", request.volume);
        return;
    }

    // Check margin requirements
    double margin_required;
    if(!OrderCalcMargin(order_type, _Symbol, request.volume, request.price, margin_required))
    {
        Print("ERROR: Failed to calculate margin requirements");
        return;
    }

    double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    if(margin_required > free_margin)
    {
        Print("ERROR: Insufficient margin. Required: ", margin_required, " Available: ", free_margin);
        return;
    }

    Print("Margin check passed. Required: ", margin_required, " Available: ", free_margin);

    // Execute trade
    if(!OrderSend(request, result))
    {
        Print("ERROR: Trade execution failed. Error: ", GetLastError());
        Print("  Retcode: ", result.retcode);
        Print("  Deal: ", result.deal);
        Print("  Order: ", result.order);
        return;
    }

    // Check execution result
    if(result.retcode == TRADE_RETCODE_DONE)
    {
        Print("TRADE EXECUTED SUCCESSFULLY:");
        Print("  Order: ", result.order);
        Print("  Deal: ", result.deal);
        Print("  Volume: ", result.volume);
        Print("  Price: ", DoubleToString(result.price, _Digits));

        // Set SL/TP after successful execution
        if(result.deal > 0)
        {
            SetSLTP(result.deal, order_type);
        }

        // Update statistics and track position
        total_trades++;
        last_trade_time = TimeCurrent();

        // Store current position info for tracking
        current_position_ticket = result.order;
        current_position_open_price = result.price;
        current_position_type = (order_type == ORDER_TYPE_BUY) ? POSITION_TYPE_BUY : POSITION_TYPE_SELL;

        Print("=== TRADE #", total_trades, " OPENED ===");
        Print("  Ticket: ", current_position_ticket);
        Print("  Type: ", (current_position_type == POSITION_TYPE_BUY) ? "BUY" : "SELL");
        Print("  Open Price: ", DoubleToString(current_position_open_price, _Digits));
        Print("  Volume: ", result.volume);
    }
    else
    {
        Print("ERROR: Trade execution failed with retcode: ", result.retcode);
        Print("  Available retcodes info:");
        Print("  TRADE_RETCODE_DONE = ", TRADE_RETCODE_DONE);
        Print("  TRADE_RETCODE_PLACED = ", TRADE_RETCODE_PLACED);
        Print("  Current retcode = ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Track position closure and calculate profit/loss                |
//+------------------------------------------------------------------+
void TrackPositionClosure()
{
    if(current_position_ticket == 0) return;

    // Check if position is still open
    if(PositionSelectByTicket(current_position_ticket))
    {
        return; // Position still open
    }

    // Position was closed, calculate profit/loss
    if(HistorySelectByPosition(current_position_ticket))
    {
        int deals = HistoryDealsTotal();
        double position_profit = 0.0;
        double close_price = 0.0;

        // Find the closing deal
        for(int i = 0; i < deals; i++)
        {
            ulong deal_ticket = HistoryDealGetTicket(i);
            if(deal_ticket > 0)
            {
                if(HistoryDealGetInteger(deal_ticket, DEAL_POSITION_ID) == current_position_ticket)
                {
                    ENUM_DEAL_ENTRY deal_entry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(deal_ticket, DEAL_ENTRY);
                    if(deal_entry == DEAL_ENTRY_OUT)
                    {
                        position_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
                        close_price = HistoryDealGetDouble(deal_ticket, DEAL_PRICE);
                        break;
                    }
                }
            }
        }

        // Update statistics
        if(position_profit > 0)
        {
            winning_trades++;
            total_profit += position_profit;
        }
        else
        {
            losing_trades++;
            total_loss += MathAbs(position_profit);
        }

        // Display trade result
        Print("=== TRADE #", total_trades, " CLOSED ===");
        Print("  Ticket: ", current_position_ticket);
        Print("  Type: ", (current_position_type == POSITION_TYPE_BUY) ? "BUY" : "SELL");
        Print("  Open Price: ", DoubleToString(current_position_open_price, _Digits));
        Print("  Close Price: ", DoubleToString(close_price, _Digits));
        Print("  Profit/Loss: $", DoubleToString(position_profit, 2));
        Print("  Result: ", (position_profit > 0) ? "WIN" : "LOSS");
        Print("--- RUNNING TOTALS ---");
        Print("  Total Trades: ", total_trades);
        Print("  Wins: ", winning_trades, " | Losses: ", losing_trades);
        Print("  Win Rate: ", DoubleToString((double)winning_trades/total_trades*100, 1), "%");
        Print("  Total Profit: $", DoubleToString(total_profit, 2));
        Print("  Total Loss: $", DoubleToString(total_loss, 2));
        Print("  Net P&L: $", DoubleToString(total_profit - total_loss, 2));
        Print("========================");

        // Reset position tracking
        current_position_ticket = 0;
        current_position_open_price = 0.0;
    }
}

//+------------------------------------------------------------------+
//| Set Stop Loss and Take Profit                                   |
//+------------------------------------------------------------------+
void SetSLTP(ulong deal_ticket, ENUM_ORDER_TYPE original_type)
{
    // Wait a moment for position to be fully opened
    Sleep(100);

    // Find the opened position by symbol and magic number
    if(!PositionSelect(_Symbol))
    {
        Print("ERROR: Cannot find opened position for SL/TP setting");
        return;
    }

    // Verify this is our position
    if(PositionGetInteger(POSITION_MAGIC) != MagicNumber)
    {
        Print("ERROR: Position magic number mismatch");
        return;
    }

    double position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    ENUM_POSITION_TYPE position_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    ulong position_ticket = PositionGetInteger(POSITION_TICKET);

    Print("Setting SL/TP for position:");
    Print("  Ticket: ", position_ticket);
    Print("  Type: ", EnumToString(position_type));
    Print("  Open Price: ", DoubleToString(position_open_price, _Digits));

    // Get current market prices
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double current_price = (position_type == POSITION_TYPE_BUY) ? bid : ask;

    // Calculate SL and TP levels with smaller, more reasonable distances
    double sl_level, tp_level;
    // For V75 1s, use direct price point values (not _Point multiplier)
    // StopLoss and TakeProfit are now in actual price points
    double sl_distance = StopLoss;  // Direct price points (e.g., 500 points)
    double tp_distance = TakeProfit; // Direct price points (e.g., 2000 points)

    Print("Setting SL/TP: SL=", DoubleToString(sl_distance, 1), " price points, TP=", DoubleToString(tp_distance, 1), " price points");
    Print("Expected profit with 0.1 lot: $", DoubleToString(tp_distance * 0.01 * FixedLot, 2));

    if(position_type == POSITION_TYPE_BUY)
    {
        sl_level = position_open_price - sl_distance;
        tp_level = position_open_price + tp_distance;
    }
    else
    {
        sl_level = position_open_price + sl_distance;
        tp_level = position_open_price - tp_distance;
    }

    // Get broker requirements
    double min_stop_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
    double freeze_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL) * _Point;

    Print("Broker Requirements:");
    Print("  Min Stop Level: ", DoubleToString(min_stop_level, _Digits));
    Print("  Freeze Level: ", DoubleToString(freeze_level, _Digits));
    Print("  Current Price: ", DoubleToString(current_price, _Digits));

    // Validate and adjust SL/TP levels
    if(position_type == POSITION_TYPE_BUY)
    {
        // For BUY positions
        if(current_price - sl_level < min_stop_level)
        {
            sl_level = current_price - min_stop_level - _Point;
            Print("WARNING: SL adjusted to minimum stop level: ", DoubleToString(sl_level, _Digits));
        }
        if(tp_level - current_price < min_stop_level)
        {
            tp_level = current_price + min_stop_level + _Point;
            Print("WARNING: TP adjusted to minimum stop level: ", DoubleToString(tp_level, _Digits));
        }
    }
    else
    {
        // For SELL positions
        if(sl_level - current_price < min_stop_level)
        {
            sl_level = current_price + min_stop_level + _Point;
            Print("WARNING: SL adjusted to minimum stop level: ", DoubleToString(sl_level, _Digits));
        }
        if(current_price - tp_level < min_stop_level)
        {
            tp_level = current_price - min_stop_level - _Point;
            Print("WARNING: TP adjusted to minimum stop level: ", DoubleToString(tp_level, _Digits));
        }
    }

    // Normalize price levels
    sl_level = NormalizeDouble(sl_level, _Digits);
    tp_level = NormalizeDouble(tp_level, _Digits);

    Print("Calculated SL/TP:");
    Print("  SL Level: ", DoubleToString(sl_level, _Digits));
    Print("  TP Level: ", DoubleToString(tp_level, _Digits));

    // Prepare modification request
    ZeroMemory(request);
    ZeroMemory(result);

    request.action = TRADE_ACTION_SLTP;
    request.symbol = _Symbol;
    request.position = position_ticket;  // Specify position ticket
    request.sl = sl_level;
    request.tp = tp_level;
    request.magic = MagicNumber;

    // Execute modification
    if(!OrderSend(request, result))
    {
        Print("ERROR: Failed to set SL/TP. Error: ", GetLastError());
        Print("  Request details:");
        Print("    Action: ", EnumToString(request.action));
        Print("    Symbol: ", request.symbol);
        Print("    Position: ", request.position);
        Print("    SL: ", DoubleToString(request.sl, _Digits));
        Print("    TP: ", DoubleToString(request.tp, _Digits));
        return;
    }

    if(result.retcode == TRADE_RETCODE_DONE)
    {
        Print("SL/TP SET SUCCESSFULLY:");
        Print("  SL: ", DoubleToString(sl_level, _Digits));
        Print("  TP: ", DoubleToString(tp_level, _Digits));
    }
    else
    {
        Print("ERROR: SL/TP modification failed with retcode: ", result.retcode);
        Print("  Result details:");
        Print("    Retcode: ", result.retcode);
        Print("    Deal: ", result.deal);
        Print("    Order: ", result.order);
    }
}

//+------------------------------------------------------------------+
//| Check and manage existing positions                             |
//+------------------------------------------------------------------+
void CheckPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetTicket(i) <= 0)
            continue;

        if(PositionGetInteger(POSITION_MAGIC) != MagicNumber)
            continue;

        if(PositionGetString(POSITION_SYMBOL) != _Symbol)
            continue;

        // Get position details
        double position_profit = PositionGetDouble(POSITION_PROFIT);
        datetime position_time = (datetime)PositionGetInteger(POSITION_TIME);
        ulong position_ticket = PositionGetInteger(POSITION_TICKET);

        bool should_close = false;
        string close_reason = "";

        // Check maximum trade duration
        if(TimeCurrent() - position_time >= MaxTradeDuration)
        {
            should_close = true;
            close_reason = "Max Duration Reached";
        }

        // Check emergency profit level
        if(position_profit >= EmergencyProfitLevel)
        {
            should_close = true;
            close_reason = "Emergency Profit Level";
        }

        // Check emergency loss level
        if(position_profit <= -EmergencyLossLevel)
        {
            should_close = true;
            close_reason = "Emergency Loss Level";
        }

        // Close position if needed
        if(should_close)
        {
            ClosePosition(position_ticket, close_reason);
        }
    }
}

//+------------------------------------------------------------------+
//| Close position by ticket                                        |
//+------------------------------------------------------------------+
void ClosePosition(ulong ticket, string reason)
{
    if(!PositionSelectByTicket(ticket))
    {
        Print("ERROR: Cannot select position for closing: ", ticket);
        return;
    }

    double position_volume = PositionGetDouble(POSITION_VOLUME);
    ENUM_POSITION_TYPE position_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    double close_price = (position_type == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Prepare close request
    ZeroMemory(request);
    ZeroMemory(result);

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = position_volume;
    request.type = (position_type == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
    request.price = close_price;
    request.deviation = 3;
    request.magic = MagicNumber;
    request.comment = TradeComment + "_CLOSE_" + reason;
    request.position = ticket;

    // Execute close
    if(!OrderSend(request, result))
    {
        Print("ERROR: Failed to close position. Error: ", GetLastError());
        return;
    }

    if(result.retcode == TRADE_RETCODE_DONE)
    {
        double profit = PositionGetDouble(POSITION_PROFIT);
        Print("POSITION CLOSED:");
        Print("  Ticket: ", ticket);
        Print("  Reason: ", reason);
        Print("  Profit: $", DoubleToString(profit, 2));

        // Update statistics
        total_profit += profit;
        if(profit > 0)
            winning_trades++;
    }
    else
    {
        Print("ERROR: Position close failed with retcode: ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Print trading statistics                                        |
//+------------------------------------------------------------------+
void PrintStatistics()
{
    if(total_trades == 0)
    {
        Print("=== TRADING STATISTICS ===");
        Print("No trades executed");
        return;
    }

    double win_rate = (double)winning_trades / total_trades * 100.0;
    // Use global losing_trades variable instead of creating local one

    Print("=== TRADING STATISTICS ===");
    Print("Total Trades: ", total_trades);
    Print("Winning Trades: ", winning_trades);
    Print("Losing Trades: ", losing_trades);
    Print("Win Rate: ", DoubleToString(win_rate, 1), "%");
    Print("Total Profit: $", DoubleToString(total_profit, 2));

    if(total_trades > 0)
    {
        double avg_profit = total_profit / total_trades;
        Print("Average Profit per Trade: $", DoubleToString(avg_profit, 2));
    }

    Print("========================");
}
