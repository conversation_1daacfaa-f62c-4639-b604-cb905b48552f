{"cells": [{"cell_type": "markdown", "id": "d90a07d7", "metadata": {}, "source": ["# Reset Notebook\n", "\n", "To ensure a completely fresh start:\n", "1. First run the cell below to clear all variables\n", "2. Then click \"Runtime\" > \"Restart runtime\" in the menu\n", "3. After restart, you can begin with your new code"]}, {"cell_type": "code", "execution_count": null, "id": "f2e2b9cc", "metadata": {}, "outputs": [], "source": ["# Clear all variables from workspace\n", "%reset -f\n", "\n", "# Clear output from all cells\n", "from IPython.display import clear_output\n", "clear_output(wait=True)\n", "\n", "print(\"Workspace cleared. Please restart the runtime now (Runtime > Restart runtime)\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}