//+------------------------------------------------------------------+
//|                                                    V75_Simple.mq5 |
//|                    V75 1s Index - SIMPLIFIED SPIKE TRADER       |
//|                    C<PERSON>AN & EFFICIENT APPROACH                   |
//+------------------------------------------------------------------+
#property copyright "2025"
#property version   "1.0"
#property strict

//=== SIMPLE SETTINGS ===
input group "=== SPIKE DETECTION ==="
input double   MinSpikeSize = 50.0;          // Minimum spike size in points
input double   MinBodyRatio = 0.3;           // Minimum body ratio
input double   MinATRMultiplier = 1.5;       // Minimum ATR multiplier

input group "=== TRADE MANAGEMENT ==="
input double   FixedLot = 0.1;               // Fixed lot size
input double   StopLoss = 30.0;              // Stop loss in points
input double   TakeProfit = 250.0;           // Take profit in points (INCREASED)
input int      CooldownSeconds = 30;         // Wait between trades
input bool     UseContrarian = false;        // Trade against spikes (false = trend following)

//=== GLOBAL VARIABLES ===
int atrHandle;
int totalTrades = 0;
int winTrades = 0;
int lossTrades = 0;
double totalProfit = 0;
datetime lastTradeTime = 0;

//+------------------------------------------------------------------+
int OnInit() {
   Print("╔══════════════════════════════════════════════════════════════╗");
   Print("║              🎯 V75 SIMPLIFIED SPIKE TRADER                  ║");
   Print("║                    CLEAN & EFFICIENT                         ║");
   Print("╚══════════════════════════════════════════════════════════════╝");

   // Create ATR indicator
   atrHandle = iATR(_Symbol, PERIOD_M1, 14);
   if(atrHandle == INVALID_HANDLE) {
      Print("❌ ERROR: Failed to create ATR indicator");
      return(INIT_FAILED);
   }

   Print("✅ EA Started - Ready to trade V75 spikes");
   Print("📊 Settings: SL=", StopLoss, " TP=", TakeProfit, " MinSpike=", MinSpikeSize);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
void OnTick() {
   static datetime lastBar = 0;
   datetime currentBar = iTime(_Symbol, PERIOD_M1, 0);
   if(lastBar == currentBar) return;
   lastBar = currentBar;

   // Check existing positions
   CheckPositions();

   // Look for new trades
   if(PositionsTotal() == 0 && (TimeCurrent() - lastTradeTime) >= CooldownSeconds) {
      CheckForSpike();
   }
}

//+------------------------------------------------------------------+
void CheckPositions() {
   for(int i = PositionsTotal() - 1; i >= 0; i--) {
      if(PositionSelectByTicket(PositionGetTicket(i))) {
         if(PositionGetInteger(POSITION_MAGIC) == 12345) {
            double currentProfit = PositionGetDouble(POSITION_PROFIT);
            datetime openTime = (datetime)PositionGetInteger(POSITION_TIME);
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            
            // Close after 5 minutes or if profit target reached
            if((TimeCurrent() - openTime) > 300 || currentProfit >= 100 || currentProfit <= -50) {
               ClosePosition(ticket, currentProfit);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void ClosePosition(ulong ticket, double profit) {
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   if(PositionSelectByTicket(ticket)) {
      request.action = TRADE_ACTION_DEAL;
      request.symbol = _Symbol;
      request.volume = PositionGetDouble(POSITION_VOLUME);
      request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
      request.price = (request.type == ORDER_TYPE_SELL) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      request.position = ticket;
      request.magic = 12345;
      request.comment = "CLOSE";

      if(OrderSend(request, result)) {
         totalProfit += profit;
         if(profit > 0) {
            winTrades++;
            Print("✅ TRADE ", totalTrades, " - WIN: $", DoubleToString(profit, 2), 
                  " | Total: $", DoubleToString(totalProfit, 2), 
                  " | WinRate: ", DoubleToString((double)winTrades/totalTrades*100, 1), "%");
         } else {
            lossTrades++;
            Print("❌ TRADE ", totalTrades, " - LOSS: $", DoubleToString(profit, 2), 
                  " | Total: $", DoubleToString(totalProfit, 2), 
                  " | WinRate: ", DoubleToString((double)winTrades/totalTrades*100, 1), "%");
         }
      }
   }
}

//+------------------------------------------------------------------+
void CheckForSpike() {
   // Get previous candle data
   double open = iOpen(_Symbol, PERIOD_M1, 1);
   double high = iHigh(_Symbol, PERIOD_M1, 1);
   double low = iLow(_Symbol, PERIOD_M1, 1);
   double close = iClose(_Symbol, PERIOD_M1, 1);

   if(open == 0 || high == 0 || low == 0 || close == 0) return;

   // Get ATR
   double atr[];
   ArraySetAsSeries(atr, true);
   if(CopyBuffer(atrHandle, 0, 0, 3, atr) < 3) return;
   double currentATR = atr[1];

   // Calculate spike metrics
   double spikeRange = (high - low) / _Point;
   double bodySize = MathAbs(close - open) / _Point;
   double bodyRatio = spikeRange > 0 ? bodySize / spikeRange : 0;
   double atrPoints = currentATR / _Point;
   double atrMultiplier = atrPoints > 0 ? spikeRange / atrPoints : 0;
   bool isBullish = close > open;

   // Validate spike
   if(spikeRange < MinSpikeSize) return;
   if(bodyRatio < MinBodyRatio) return;
   if(atrMultiplier < MinATRMultiplier) return;

   // Determine trade direction
   ENUM_ORDER_TYPE tradeType = UseContrarian ? 
      (isBullish ? ORDER_TYPE_SELL : ORDER_TYPE_BUY) : 
      (isBullish ? ORDER_TYPE_BUY : ORDER_TYPE_SELL);

   // Execute trade
   ExecuteTrade(tradeType, spikeRange);
}

//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE type, double spikeSize) {
   double price = (type == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
   
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = FixedLot;
   request.type = type;
   request.price = NormalizeDouble(price, _Digits);
   request.sl = 0;  // Set after order placement
   request.tp = 0;  // Set after order placement
   request.magic = 12345;
   request.comment = "SPIKE_" + (string)(totalTrades + 1);

   if(OrderSend(request, result)) {
      totalTrades++;
      lastTradeTime = TimeCurrent();
      
      // Set SL/TP
      if(result.deal > 0) {
         SetSLTP(result.deal, type, price);
      }

      Print("🎯 TRADE ", totalTrades, ": ", EnumToString(type), 
            " at ", DoubleToString(price, _Digits), 
            " | Spike: ", DoubleToString(spikeSize, 1), " pts",
            " | Ticket: ", result.order);
   } else {
      Print("❌ TRADE FAILED: ", result.retcode, " - ", result.comment);
   }
}

//+------------------------------------------------------------------+
void SetSLTP(ulong dealTicket, ENUM_ORDER_TYPE originalType, double entryPrice) {
   // Find the position
   for(int i = 0; i < PositionsTotal(); i++) {
      if(PositionSelectByTicket(PositionGetTicket(i))) {
         if(PositionGetInteger(POSITION_MAGIC) == 12345) {
            double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
            long minStopLevel = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
            double minDistance = MathMax(minStopLevel * point, 20 * point);
            
            // Use actual entry price for calculations
            double positionPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            // Calculate SL/TP distances - force our TP setting, keep SL reasonable
            double slDistance = MathMax(StopLoss * point, minDistance);
            double tpDistance = TakeProfit * point;  // Force our 250 points
            
            double newSL, newTP;
            if(posType == POSITION_TYPE_BUY) {
               newSL = positionPrice - slDistance;
               newTP = positionPrice + tpDistance;  // 250 points from entry
            } else {
               newSL = positionPrice + slDistance;
               newTP = positionPrice - tpDistance;  // 250 points from entry
            }
            
            newSL = NormalizeDouble(newSL, _Digits);
            newTP = NormalizeDouble(newTP, _Digits);
            
            // Modify position
            MqlTradeRequest modifyRequest = {};
            MqlTradeResult modifyResult = {};
            
            modifyRequest.action = TRADE_ACTION_SLTP;
            modifyRequest.symbol = _Symbol;
            modifyRequest.position = PositionGetInteger(POSITION_TICKET);
            modifyRequest.sl = newSL;
            modifyRequest.tp = newTP;
            
            if(OrderSend(modifyRequest, modifyResult)) {
               Print("✅ SL/TP Set: SL=", newSL, " TP=", newTP, 
                     " | TP Distance: ", DoubleToString(tpDistance/_Point, 1), " pts");
            } else {
               Print("❌ Failed to set SL/TP: ", modifyResult.retcode);
            }
            break;
         }
      }
   }
}

//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction &trans, const MqlTradeRequest &request, const MqlTradeResult &result) {
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {
      if(HistoryDealSelect(trans.deal)) {
         string comment = HistoryDealGetString(trans.deal, DEAL_COMMENT);
         if(StringFind(comment, "SPIKE_") >= 0) {
            double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(trans.deal, DEAL_TYPE);
            
            if(profit != 0 || HistoryDealGetInteger(trans.deal, DEAL_ENTRY) == DEAL_ENTRY_OUT) {
               totalProfit += profit;
               if(profit > 0) {
                  winTrades++;
                  Print("✅ TRADE ", totalTrades, " - WIN: $", DoubleToString(profit, 2), 
                        " | Total: $", DoubleToString(totalProfit, 2), 
                        " | WinRate: ", DoubleToString((double)winTrades/totalTrades*100, 1), "%");
               } else {
                  lossTrades++;
                  Print("❌ TRADE ", totalTrades, " - LOSS: $", DoubleToString(profit, 2), 
                        " | Total: $", DoubleToString(totalProfit, 2), 
                        " | WinRate: ", DoubleToString((double)winTrades/totalTrades*100, 1), "%");
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
   
   Print("╔══════════════════════════════════════════════════════════════╗");
   Print("║                    EA STOPPED                                ║");
   Print("║  Total Trades: ", totalTrades, " | Wins: ", winTrades, " | Losses: ", lossTrades, "               ║");
   Print("║  Total Profit: $", DoubleToString(totalProfit, 2), "                                    ║");
   Print("║  Win Rate: ", DoubleToString((double)winTrades/totalTrades*100, 1), "%                                         ║");
   Print("╚══════════════════════════════════════════════════════════════╝");
}